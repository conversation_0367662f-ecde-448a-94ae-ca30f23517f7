/**
 * router/index.js
 *
 * Vue Router configuration
 */

import { createRouter, createWebHashHistory } from "vue-router"
import baseRoutes from "./base.routes"
import { useAuthStore } from "@/stores/authstore"

const router = createRouter({
  history: createWebHashHistory(),
  routes: [...baseRoutes],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // 初始化认证状态
  authStore.initAuth()

  // 公开路由（不需要认证的路由）
  const publicRoutes = ['login']
  const isPublicRoute = publicRoutes.includes(to.name)

  // 检查路由是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)


  if (!isPublicRoute && (requiresAuth || to.path === '/')) {
    // 需要认证的路由，检查登录状态
    if (!authStore.isLoggedIn) {

      next({ name: 'login' })
      return
    }
  }

  if (to.name === 'login' && authStore.isLoggedIn) {
    // 已登录用户访问登录页面，重定向到首页

    next({ name: 'home' })
    return
  }

  // 正常访问
  next()
})

export default router
