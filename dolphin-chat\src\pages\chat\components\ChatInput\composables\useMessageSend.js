import { ref, nextTick } from "vue"
import { useChatStore } from "@/stores/baseStore"
import { ChatService } from "@/api/chat"

/**
 * 消息发送相关逻辑
 */
export function useMessageSend() {
  const chatStore = useChatStore()

  // 响应式数据
  const deepThinkActive = ref(false)
  const webSearchActive = ref(false)

  /**
   * 发送消息
   * @param {string} message - 消息内容
   * @param {Array} attachedImages - 附件图片
   * @param {Function} clearAttachedImages - 清空附件的回调
   * @param {Function} resetTextareaHeight - 重置输入框高度的回调
   * @param {Function} scrollToBottomImmediate - 滚动到底部的回调
   */
  const handleSend = async (
    message,
    attachedImages,
    clearAttachedImages,
    resetTextareaHeight,
    scrollToBottomImmediate
  ) => {
    if ((!message.trim() && attachedImages.length === 0) || chatStore.isLoading) return

    const userMessage = message.trim()

    // 检查是否有图片正在上传
    const hasUploadingImages = attachedImages.some((img) => img.uploading)
    if (hasUploadingImages) {
      console.warn("⚠️ 有图片正在上传中，请等待上传完成后再发送")
      return
    }

    // 检查是否有上传失败的图片
    const hasFailedImages = attachedImages.some((img) => img.uploadError)
    if (hasFailedImages) {
      console.warn("⚠️ 有图片上传失败，请重新上传后再发送")
      return
    }

    // 收集已上传成功的图片ID
    const uploadedImages = attachedImages
      .filter((img) => img.uploaded && img.serverData)
      .map((img) => {
        // 提取图片ID，支持多种可能的返回格式
        if (typeof img.serverData === "string") {
          // 如果直接返回字符串ID
          return img.serverData
        } else if (img.serverData && typeof img.serverData === "object") {
          // 如果返回对象，尝试提取ID字段
          return (
            img.serverData.id || img.serverData.imageId || img.serverData.data || img.serverData
          )
        }
        return img.serverData
      })
      .filter((id) => id) // 过滤掉空值

    // 先发送图片消息到界面显示
    if (attachedImages.length > 0) {
      if (attachedImages.length === 1) {
        // 单张图片使用原有逻辑
        chatStore.addImageMessage(attachedImages[0], userMessage)
      } else {
        // 多张图片使用新的多图片消息类型
        chatStore.addMultiImageMessage(attachedImages, userMessage)
      }
      // 清空附件
      clearAttachedImages()
    } else if (userMessage) {
      // 发送文本消息

      chatStore.addUserMessage(userMessage)
    }

    // 确保不再显示欢迎页面
    chatStore.setShowWelcome(false)

    // 立即滚动到底部（用户消息）
    nextTick(() => {
      scrollToBottomImmediate()
    })

    // 重置textarea高度到初始状态
    resetTextareaHeight()

    // 设置加载状态
    chatStore.setLoading(true)

    try {
      // 调用流式AI API，包含图片ID
      const requestData = {
        question: userMessage,
        // 将上传成功的图片ID作为images数组传递
        images: uploadedImages,
        // 深度思考参数
        deepThinking: deepThinkActive.value,
        // 添加模型字段
        model: chatStore.selectedModel,
      }


      // 创建AI消息占位符
      const aiMessage = chatStore.addAIMessage("")
      let accumulatedContent = ""

      // 调用流式API
      const result = await ChatService.getAIReplyStream(
        requestData,
        // onMessage - 处理流式数据
        (data) => {
          // 直接使用原始数据，不做任何转换
          if (data) {
            accumulatedContent += data
            // 更新AI消息内容
            try {
              if (chatStore && typeof chatStore.updateAIMessage === "function") {
                chatStore.updateAIMessage(aiMessage.id, accumulatedContent)
              } else {
                console.error("❌ chatStore.updateAIMessage 方法不存在:", {
                  chatStore: !!chatStore,
                  chatStoreType: typeof chatStore,
                  updateAIMessageType: typeof chatStore?.updateAIMessage,
                  chatStoreKeys: Object.keys(chatStore || {}),
                  chatStoreProto: Object.getPrototypeOf(chatStore || {}),
                  aiMessageId: aiMessage?.id,
                })
              }
            } catch (error) {
              console.error("❌ 更新AI消息时发生错误:", error)
            }

            // 滚动到底部
            nextTick(() => {
              scrollToBottomImmediate()
            })
          }
        },
        // onError - 处理错误
        (error) => {
          console.error("❌ 流式AI回复失败:", error)
          const errorMessage = `❌ ${error.message || "AI服务暂时不可用，请稍后再试。"}`

          if (accumulatedContent) {
            // 如果已有部分内容，追加错误信息
            if (chatStore && typeof chatStore.updateAIMessage === "function") {
              chatStore.updateAIMessage(aiMessage.id, accumulatedContent + "\n\n" + errorMessage)
            }
          } else {
            // 如果没有内容，直接显示错误
            if (chatStore && typeof chatStore.updateAIMessage === "function") {
              chatStore.updateAIMessage(aiMessage.id, errorMessage)
            }
          }

          chatStore.setLoading(false)
        },
        // onComplete - 完成回调
        () => {

          chatStore.setLoading(false)

          // 最终滚动到底部
          nextTick(() => {
            scrollToBottomImmediate()
          })
        }
      )

      if (!result.success) {
        // 流式请求启动失败
        const errorMessage =
          result.error || result.message || "抱歉，AI服务暂时不可用，请稍后再试。"
        if (chatStore && typeof chatStore.updateAIMessage === "function") {
          chatStore.updateAIMessage(aiMessage.id, `❌ ${errorMessage}`)
        }
        console.error("❌ 流式AI请求启动失败:", result)
        chatStore.setLoading(false)
      }
    } catch (error) {
      console.error("❌ 发送消息失败:", error)

      // 根据错误类型显示不同的错误信息
      let errorMessage = "抱歉，发生了错误，请稍后再试。"

      if (error.code === "NETWORK_ERROR" || error.message.includes("Network Error")) {
        errorMessage = "网络连接失败，请检查网络设置后重试。"
      } else if (error.response?.status === 401) {
        errorMessage = "身份验证失败，请重新登录。"
      } else if (error.response?.status === 429) {
        errorMessage = "请求过于频繁，请稍后再试。"
      } else if (error.response?.status >= 500) {
        errorMessage = "服务器暂时不可用，请稍后再试。"
      }

      chatStore.addAIMessage(`❌ ${errorMessage}`)
    } finally {
      chatStore.setLoading(false)
    }
  }

  /**
   * 处理深度思考
   */
  const handleDeepThink = () => {

    deepThinkActive.value = !deepThinkActive.value
    // 这里可以添加深度思考逻辑
  }

  /**
   * 处理联网搜索
   */
  const handleWebSearch = () => {

    webSearchActive.value = !webSearchActive.value
    // 这里可以添加联网搜索逻辑
  }

  /**
   * 处理键盘按下事件
   * @param {KeyboardEvent} event - 键盘事件
   * @param {Function} sendCallback - 发送消息的回调
   */
  const handleKeydown = (event, sendCallback) => {
    // Enter 发送消息 (不需要Ctrl)
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      sendCallback()
    }
  }

  return {
    // 响应式数据
    deepThinkActive,
    webSearchActive,

    // 方法
    handleSend,
    handleDeepThink,
    handleWebSearch,
    handleKeydown,
  }
}
