{"compilerOptions": {"target": "esnext", "module": "esnext", "baseUrl": "./", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "jsx": "preserve", "declaration": false, "declarationMap": false, "sourceMap": true, "removeComments": false, "noEmit": true, "importHelpers": true, "downlevelIteration": true, "experimentalDecorators": true, "resolveJsonModule": true, "isolatedModules": true, "noImplicitAny": false, "skipLibCheck": true, "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/pages/*": ["src/pages/*"], "@/layouts/*": ["src/layouts/*"], "@/stores/*": ["src/stores/*"], "@/utils/*": ["src/utils/*"], "@/api/*": ["src/api/*"], "@/assets/*": ["src/assets/*"], "@/plugins/*": ["src/plugins/*"], "@/router/*": ["src/router/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost", "webworker"], "types": ["vite/client", "node"]}, "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*", "vite.config.*", "vitest.config.*", "cypress.config.*", "playwright.config.*"], "exclude": ["node_modules", "dist", "build", "coverage", "**/*.d.ts"]}