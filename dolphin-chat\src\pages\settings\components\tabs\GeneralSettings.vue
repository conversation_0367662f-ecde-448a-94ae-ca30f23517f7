<script setup>
import { ref, computed, watch } from "vue"
import { useTheme } from "@/composables/useTheme"

// 使用主题管理
const {
  themeMode,
  themeDisplayName,
  themeOptions,
  setTheme,
  getThemeIcon,
  getThemeColor
} = useTheme()

// 响应式数据
const language = ref("跟随系统")

// 主题相关的计算属性
const selectedTheme = computed({
  get: () => themeMode.value,
  set: (value) => setTheme(value)
})

// 选项数据
const languageOptions = [
  { title: "跟随系统", value: "system" },
  { title: "简体中文", value: "zh-CN" },
  { title: "English", value: "en" },
  { title: "繁體中文", value: "zh-TW" }
]

// 主题选项（带图标）
const themeSelectOptions = themeOptions.map(option => ({
  title: option.label,
  value: option.value,
  props: {
    prependIcon: option.icon
  }
}))

// 监听主题变化，提供用户反馈
watch(selectedTheme, (newTheme, oldTheme) => {
  if (oldTheme && newTheme !== oldTheme) {
    // 可以在这里添加主题切换的提示或动画
    console.log(`主题已切换: ${oldTheme} -> ${newTheme}`)
  }
})
</script>

<template>
  <div class="tab-panel">
    <!-- 语言设置 -->
    <div class="setting-row">
      <span class="setting-label">语言</span>
      <VSelect
        v-model="language"
        :items="languageOptions"
        variant="outlined"
        density="compact"
        class="deepseek-select"
        hide-details />
    </div>

    <!-- 主题设置 -->
    <div class="setting-row">
      <div class="setting-info">
        <span class="setting-label">主题</span>
        <span class="setting-description">当前: {{ themeDisplayName }}</span>
      </div>
      <VSelect
        v-model="selectedTheme"
        :items="themeSelectOptions"
        variant="outlined"
        density="compact"
        class="deepseek-select theme-select"
        hide-details>
        <template #selection="{ item }">
          <div class="theme-selection">
            <VIcon
              :icon="getThemeIcon(item.value)"
              :color="getThemeColor(item.value)"
              size="small"
              class="mr-2" />
            <span>{{ item.title }}</span>
          </div>
        </template>
        <template #item="{ item, props }">
          <VListItem v-bind="props" class="theme-option" :title="item.title">
            <template #prepend>
              <VIcon
                :icon="getThemeIcon(item.value)"
                :color="getThemeColor(item.value)"
                size="small" />
            </template>
          </VListItem>
        </template>
      </VSelect>
    </div>
  </div>
</template>

<style scoped>
.tab-panel {
  max-width: 100%;
}

.setting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 0;
  border-bottom: 1px solid var(--app-border-light);
  transition: border-color 0.3s ease;
}

.setting-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.setting-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.setting-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--app-text-primary);
  min-width: 80px;
  transition: color 0.3s ease;
}

.setting-description {
  font-size: 12px;
  color: var(--app-text-secondary);
  font-weight: 400;
  transition: color 0.3s ease;
}

/* 主题选择器样式 */
.theme-select {
  min-width: 180px;
}

.theme-selection {
  display: flex;
  align-items: center;
}

.theme-option {
  transition: background-color 0.2s ease;
}

.theme-option:hover {
  background-color: var(--app-hover-bg);
}

.deepseek-select {
  max-width: 200px;
  min-width: 150px;
  border-radius: 4px;
}
</style>
