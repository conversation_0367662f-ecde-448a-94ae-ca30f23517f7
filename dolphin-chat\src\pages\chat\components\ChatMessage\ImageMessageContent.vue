<template>
  <div class="image-content">
    <div class="image-container">
      <img
        :src="imageUrl"
        :alt="imageName || '图片'"
        class="message-image"
        @click="handleImageClick"
        @error="handleImageError" />
      <div class="image-overlay">
        <VBtn
          icon="mdi-fullscreen-exit"
          size="small"
          variant="text"
          color="white"
          @click="handleImageClick" />
      </div>
    </div>
    <div v-if="caption" class="image-caption">
      {{ caption }}
    </div>
  </div>
</template>

<script setup>
import { useImageHandling } from './composables/useImageHandling'

// 接收props
const props = defineProps({
  imageUrl: {
    type: String,
    required: true,
  },
  imageName: {
    type: String,
    default: '',
  },
  caption: {
    type: String,
    default: '',
  },
})

// 定义emits
const emit = defineEmits(['open-preview'])

// 使用 composable
const { handleImageError } = useImageHandling()

// 事件处理
const handleImageClick = () => {
  emit('open-preview')
}
</script>

<style scoped>
.image-content {
  max-width: 350px;
  position: relative;
}

.image-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  margin-bottom: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.9);
}

.image-container:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
  transform: translateY(-4px);
}

.message-image {
  width: 100%;
  height: auto;
  max-height: 350px;
  object-fit: cover;
  display: block;
  transition: all 0.3s ease;
}

.image-container:hover .message-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 12px;
  right: 12px;
  opacity: 0;
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  padding: 8px;
}

.image-container:hover .image-overlay {
  opacity: 1;
}

.image-overlay .v-btn {
  background: transparent !important;
  box-shadow: none !important;
}

.image-caption {
  font-size: 14px;
  color: #4a5568;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 16px;
  margin-top: 8px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  line-height: 1.5;
  font-weight: 400;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-content {
    max-width: 280px;
  }

  .message-image {
    max-height: 250px;
  }

  .image-container {
    border-radius: 16px;
  }
}

@media (max-width: 480px) {
  .image-content {
    max-width: 240px;
  }

  .message-image {
    max-height: 200px;
  }
}
</style>
