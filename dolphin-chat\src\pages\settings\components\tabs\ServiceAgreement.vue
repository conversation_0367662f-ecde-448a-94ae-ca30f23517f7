<script setup>
// 服务协议相关数据
const userAgreement = {
  title: "用户协议",
  content: [
    "欢迎使用 Dolphin AI 服务。在使用我们的服务之前，请仔细阅读以下用户协议：",
    "1. 服务条款：用户在使用本服务时，应遵守相关法律法规",
    "2. 隐私政策：我们重视用户隐私，承诺保护用户个人信息",
    "3. 免责声明：本服务仅供参考，不构成专业建议"
  ]
}

const privacyPolicy = {
  title: "隐私政策",
  content: [
    "我们收集和使用您的个人信息仅用于提供更好的服务体验",
    "您的数据将被安全存储，不会与第三方共享"
  ]
}
</script>

<template>
  <div class="tab-panel">
    <div class="service-section">
      <h3 class="section-title">{{ userAgreement.title }}</h3>
      <div class="agreement-content">
        <p v-for="(paragraph, index) in userAgreement.content" :key="index">
          {{ paragraph }}
        </p>
      </div>
    </div>

    <div class="service-section">
      <h3 class="section-title">{{ privacyPolicy.title }}</h3>
      <div class="agreement-content">
        <p v-for="(paragraph, index) in privacyPolicy.content" :key="index">
          {{ paragraph }}
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.tab-panel {
  max-width: 100%;
}

.service-section {
  margin-bottom: 32px;
  padding: 0;
}

.service-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--app-text-primary);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--app-border-light);
  transition: color 0.3s ease, border-color 0.3s ease;
}

.agreement-content {
  font-size: 14px;
  line-height: 1.6;
  color: var(--app-text-secondary);
  max-height: 200px;
  overflow-y: auto;
  padding: 0;
  transition: color 0.3s ease;
}

.agreement-content p {
  margin-bottom: 12px;
  padding: 0;
}

.agreement-content p:last-child {
  margin-bottom: 0;
}

.agreement-content::-webkit-scrollbar {
  width: 4px;
}

.agreement-content::-webkit-scrollbar-track {
  background: transparent;
}

.agreement-content::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-bg);
  border-radius: 2px;
}

.agreement-content::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-bg);
}
</style>
