<template>
  <div class="text-content">
    <!-- eslint-disable-next-line vue/no-v-html -->
    <div class="text-rendered" v-html="htmlContent"></div>
  </div>
</template>

<script setup>
import { computed } from "vue"
import { renderMarkdown } from "@/utils/markdown"

// 接收props
const props = defineProps({
  content: {
    type: String,
    required: true,
  },
})

// 计算属性 - Markdown 渲染
const htmlContent = computed(() => {
  if (!props.content) return ""
  // 使用 markdown-it 进行渲染
  return renderMarkdown(props.content)
})
</script>

<style lang="css" scoped>
.text-content {
  font-size: 15px;
  color: #1f2937;
  line-height: 1.6;
  font-weight: 400;
}

/* Markdown 元素样式 */

.text-rendered :deep(h1),
.text-rendered :deep(h2),
.text-rendered :deep(h3),
.text-rendered :deep(h4),
.text-rendered :deep(h5),
.text-rendered :deep(h6) {
  margin: 6px 0 2px 0;
  font-weight: 600;
  line-height: 1.2;
  color: #1f2937;
}

.text-rendered :deep(h1) {
  font-size: 24px;
}
.text-rendered :deep(h2) {
  font-size: 20px;
}
.text-rendered :deep(h3) {
  font-size: 18px;
}
.text-rendered :deep(h4) {
  font-size: 16px;
}
.text-rendered :deep(h5) {
  font-size: 14px;
}
.text-rendered :deep(h6) {
  font-size: 13px;
}

.text-rendered :deep(p) {
  margin: 2px 0;
  line-height: 1.4;
}

.text-rendered :deep(ul),
.text-rendered :deep(ol) {
  margin: 2px 0;
  padding-left: 16px;
}

.text-rendered :deep(li) {
  margin: 1px 0;
  line-height: 1.3;
}

.text-rendered :deep(blockquote) {
  margin: 6px 0;
  padding: 6px 12px;
  border-left: 3px solid #e5e7eb;
  background-color: #f9fafb;
  color: #6b7280;
  font-style: italic;
}

.text-rendered :deep(code) {
  background-color: #f3f4f6;
  color: #e11d48;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: "Consolas", "Monaco", "Courier New", monospace;
  font-size: 13px;
}

.text-rendered :deep(pre) {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 6px 0;
  font-family: "Consolas", "Monaco", "Courier New", monospace;
  font-size: 13px;
  line-height: 1.3;
}

.text-rendered :deep(pre code) {
  background: none;
  color: inherit;
  padding: 0;
  border-radius: 0;
}

.text-rendered :deep(table) {
  border-collapse: collapse;
  margin: 6px 0;
  width: 100%;
}

.text-rendered :deep(th),
.text-rendered :deep(td) {
  border: 1px solid #e5e7eb;
  padding: 6px 10px;
  text-align: left;
}

.text-rendered :deep(th) {
  background-color: #f9fafb;
  font-weight: 600;
}

.text-rendered :deep(a) {
  color: #3b82f6;
  text-decoration: none;
}

.text-rendered :deep(a:hover) {
  text-decoration: underline;
}

.text-rendered :deep(strong) {
  font-weight: 600;
}

.text-rendered :deep(em) {
  font-style: italic;
}

.text-rendered :deep(hr) {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 8px 0;
}

/* 自定义容器样式 */
.text-rendered :deep(.custom-block) {
  margin: 6px 0;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid;
}

.text-rendered :deep(.custom-block-title) {
  font-weight: 600;
  margin: 0 0 4px 0;
}

.text-rendered :deep(.warning) {
  background-color: #fef3cd;
  border-left-color: #f59e0b;
  color: #92400e;
}

.text-rendered :deep(.tip) {
  background-color: #dbeafe;
  border-left-color: #3b82f6;
  color: #1e40af;
}
</style>
