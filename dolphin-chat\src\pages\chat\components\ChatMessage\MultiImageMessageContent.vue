<template>
  <div class="multi-image-content">
    <!-- 图片网格容器 -->
    <div
      class="images-grid"
      :class="{
        'grid-single': images.length === 1,
        'grid-two': images.length === 2,
        'grid-three': images.length === 3,
        'grid-four': images.length === 4
      }">
      <div
        v-for="(image, index) in displayImages"
        :key="image.id || index"
        class="image-item"
        @click="handleImageClick(index)">
        <img
          :src="image.url"
          :alt="image.name || `图片${index + 1}`"
          class="message-image"
          @error="handleImageError" />

        <!-- 由于限制最多4张图片，不再需要显示更多图片的提示 -->

        <!-- 放大图标 -->
        <div class="image-overlay">
          <VBtn
            icon="mdi-fullscreen-exit"
            size="small"
            variant="text"
            color="white"
            @click.stop="handleImageClick(index)" />
        </div>
      </div>
    </div>

    <!-- 图片说明文字 -->
    <div v-if="caption" class="image-caption">
      {{ caption }}
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useImageHandling } from './composables/useImageHandling'

// 接收props
const props = defineProps({
  images: {
    type: Array,
    required: true,
    default: () => []
  },
  caption: {
    type: String,
    default: '',
  },
})

// 定义emits
const emit = defineEmits(['open-preview'])

// 使用 composable
const { handleImageError } = useImageHandling()

// 计算显示的图片（由于限制最多4张，直接显示所有图片）
const displayImages = computed(() => {
  return props.images
})

// 事件处理
const handleImageClick = (index) => {
  emit('open-preview', {
    images: props.images,
    currentIndex: index
  })
}
</script>

<style scoped>
.multi-image-content {
  max-width: 400px;
  position: relative;
}

.images-grid {
  display: grid;
  gap: 8px;
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 12px;
}

/* 单张图片 */
.grid-single {
  grid-template-columns: 1fr;
}

/* 两张图片 */
.grid-two {
  grid-template-columns: 1fr 1fr;
}

/* 三张图片 */
.grid-three {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.grid-three .image-item:first-child {
  grid-column: 1 / -1;
}

/* 四张图片 */
.grid-four {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.image-item {
  position: relative;
  cursor: pointer;
  overflow: hidden;
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.image-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.message-image {
  width: 100%;
  height: 100%;
  min-height: 120px;
  max-height: 200px;
  object-fit: cover;
  display: block;
  transition: all 0.3s ease;
}

.grid-single .message-image {
  max-height: 300px;
}

.image-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-item:hover .image-overlay {
  opacity: 1;
}



.image-caption {
  color: #666;
  font-size: 14px;
  line-height: 1.4;
  margin-top: 8px;
  padding: 0 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .multi-image-content {
    max-width: 100%;
  }

  .message-image {
    min-height: 100px;
    max-height: 150px;
  }

  .grid-single .message-image {
    max-height: 250px;
  }
}
</style>
