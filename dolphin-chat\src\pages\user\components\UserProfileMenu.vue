<script setup>
import { ref } from "vue"
import { useRouter } from "vue-router"
import { useAuthStore } from "@/stores/authstore"
import informationIconUrl from "@/assets/icons/Information.svg"

// Props
const props = defineProps({
  isCollapsed: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(["settings-open"])

// 状态管理
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const userMenuOpen = ref(false)

// 方法
const openProfile = () => {
  userMenuOpen.value = !userMenuOpen.value
}

const openSettings = () => {
  userMenuOpen.value = false
  emit("settings-open")
}

const openContact = () => {
  userMenuOpen.value = false
  console.log("联系我们");

}

const handleLogout = () => {
  userMenuOpen.value = false
  authStore.logout()
  router.push({ name: "login" })
}
</script>

<template>
  <!-- 展开状态的个人资料菜单 -->
  <VMenu
    v-if="!isCollapsed"
    v-model="userMenuOpen"
    :close-on-content-click="false"
    location="top end"
    offset="8">
    <template #activator="{ props }">
      <VListItem
        v-bind="props"
        title="个人资料"
        :append-icon="userMenuOpen ? 'mdi-chevron-up' : 'mdi-chevron-down'">
        <template #prepend>
          <VAvatar size="24" class="profile-icon-avatar">
            <img :src="informationIconUrl" alt="个人资料" class="profile-icon" />
          </VAvatar>
        </template>
      </VListItem>
    </template>

    <VCard min-width="200" class="user-menu-card">
      <!-- 菜单项 -->
      <VList density="compact" class="user-menu-list">
        <VListItem
          prepend-icon="mdi-cog-outline"
          title="系统设置"
          class="menu-item"
          @click="openSettings" />
        <VListItem
          prepend-icon="mdi-email-outline"
          title="联系我们"
          class="menu-item"
          @click="openContact" />
        <VListItem
          prepend-icon="mdi-logout"
          title="退出登录"
          class="menu-item"
          @click="handleLogout" />
      </VList>
    </VCard>
  </VMenu>

  <!-- 收缩状态的个人资料菜单 -->
  <VMenu
    v-if="isCollapsed"
    v-model="userMenuOpen"
    :close-on-content-click="false"
    location="top end"
    offset="8">
    <template #activator="{ props }">
      <VBtn
        v-bind="props"
        variant="text"
        size="default"
        class="collapsed-menu-btn profile-icon-btn">
        <img :src="informationIconUrl" alt="个人资料" class="profile-icon-small" />
      </VBtn>
    </template>

    <VCard min-width="200" class="user-menu-card">
      <!-- 菜单项 -->
      <VList density="compact" class="user-menu-list">
        <VListItem
          prepend-icon="mdi-cog-outline"
          title="系统设置"
          class="menu-item"
          @click="openSettings" />
        <VListItem
          prepend-icon="mdi-email-outline"
          title="联系我们"
          class="menu-item"
          @click="openContact" />
        <VListItem
          prepend-icon="mdi-logout"
          title="退出登录"
          class="menu-item"
          @click="handleLogout" />
      </VList>
    </VCard>
  </VMenu>
</template>

<style scoped>
/* 用户菜单样式 */
.user-menu-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
}

.user-menu-list {
  padding: 8px 0;
}

.menu-item {
  padding: 8px 16px;
  min-height: 40px;
}

.menu-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.menu-item .v-list-item__prepend {
  margin-right: 12px;
}

.menu-item .v-icon {
  color: #666;
}

.collapsed-menu-btn {
  width: 100%;
  color: rgb(var(--v-theme-on-surface)) !important;
  opacity: 0.8;
}

.collapsed-menu-btn:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.05);
}

/* 个人资料图标样式 */
.profile-icon-avatar {
  background-color: transparent !important;
}

.profile-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
  /* filter样式现在由主题CSS统一管理 */
}

.profile-icon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  min-width: 44px;
  border-radius: 8px;
  padding: 0;
  margin: 0 auto;
}

.profile-icon-small {
  width: 20px;
  height: 20px;
  object-fit: contain;
  /* filter样式现在由主题CSS统一管理 */
}
</style>
