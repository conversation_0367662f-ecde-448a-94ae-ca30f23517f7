<script setup>
import { computed, inject } from "vue"
import { useChatStore } from '@/stores/baseStore'

// 导入子组件
import MessageAvatar from './ChatMessage/MessageAvatar.vue'
import MessageContent from './ChatMessage/MessageContent.vue'
import MessageFooter from './ChatMessage/MessageFooter.vue'
import ImagePreviewDialog from './ChatMessage/ImagePreviewDialog.vue'

// 导入 composables
import { useImageHandling } from './ChatMessage/composables/useImageHandling'

// 接收消息数据
const props = defineProps({
  message: {
    type: Object,
    required: true,
  },
})

// 使用store
const chatStore = useChatStore()

// 使用 composables
const {
  showImageDialog,
  openImagePreview,
  closeImagePreview,
  downloadImage,
} = useImageHandling()

// 计算属性
const isUser = computed(() => props.message.type === "user")
const isAI = computed(() => props.message.type === "ai")
const isImage = computed(() => props.message.type === "image" || props.message.type === "multi-image")

// 计算AI消息对应的用户询问图片
const userImages = computed(() => {
  if (!isAI.value) return []

  // 查找AI消息对应的用户询问
  const userMessage = chatStore.findUserMessageForAI(props.message.id)
  if (!userMessage) return []

  // 收集用户询问中的图片
  const images = []

  // 检查单张图片消息
  if (userMessage.type === 'image' && userMessage.imageUrl) {
    images.push({
      url: userMessage.imageUrl,
      name: userMessage.imageName,
      id: userMessage.id
    })
  }

  // 检查多张图片消息
  if (userMessage.type === 'multi-image' && userMessage.images) {
    userMessage.images.forEach(img => {
      images.push({
        url: img.url,
        name: img.name,
        id: img.id
      })
    })
  }

  return images
})

// 事件处理
const handleOpenImagePreview = () => {
  openImagePreview()
}

const handleCloseImagePreview = () => {
  closeImagePreview()
}

const handleDownloadImage = () => {
  downloadImage(props.message.imageUrl, props.message.imageName)
}

const handleOpenMultiImagePreview = (data) => {
  // 处理多图片预览
  console.log('打开多图片预览:', data)
  // 这里可以实现多图片预览的逻辑
}

// 新增：处理显示用户询问图片到画布
const handleShowUserImages = (images) => {
  console.log('显示用户询问图片到画布:', images)
  // 将图片添加到画布并显示
  chatStore.addImagesToCanvas(images)
}


</script>

<template>
  <div
    :id="`message-${message.id}`"
    class="message-container"
    :class="{ 'user-message': isUser, 'ai-message': isAI, 'image-message': isImage }">
    <div class="message-wrapper">
      <!-- AI消息的头像 -->
      <MessageAvatar
        v-if="isAI"
        :message-type="message.type" />

      <!-- 消息内容区域 -->
      <div class="message-content-wrapper">
        <!-- 消息内容 -->
        <MessageContent
          :message="message"
          :message-type="message.type"
          @open-image-preview="handleOpenImagePreview"
          @open-multi-image-preview="handleOpenMultiImagePreview" />

        <!-- 消息底部（操作按钮） -->
        <MessageFooter
          :message-type="message.type"
          :content="message.content"
          :image-url="message.imageUrl"
          :image-name="message.imageName"
          :user-images="userImages"
          @download-image="handleDownloadImage"
          @open-image-preview="handleOpenImagePreview"
          @show-user-images="handleShowUserImages" />
      </div>

      <!-- 用户消息的头像 -->
      <MessageAvatar
        v-if="isUser"
        :message-type="message.type" />
    </div>

    <!-- 图片预览对话框 -->
    <ImagePreviewDialog
      :show="showImageDialog"
      :image-url="message.imageUrl"
      :image-name="message.imageName"
      @close="handleCloseImagePreview"
      @download="handleDownloadImage" />
  </div>
</template>


<style scoped>
.message-container {
  margin-bottom: 32px;
  width: 100%;
  animation: fadeInUp 0.4s ease-out;
}

/* 最后一条消息增加额外的底部边距 */
.message-container:last-child {
  margin-bottom: 60px;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
}

/* AI消息样式 */
.ai-message .message-wrapper {
  justify-content: flex-start;
}

/* 用户消息样式 */
.user-message .message-wrapper {
  justify-content: flex-end;
}

/* 图片消息样式 */
.image-message .message-wrapper {
  justify-content: flex-end;
}

/* 消息内容包装器 */
.message-content-wrapper {
  max-width: 92%;
  min-width: 120px;
  position: relative;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-wrapper {
    padding: 0 12px;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .message-wrapper {
    padding: 0 8px;
    gap: 10px;
  }
}


</style>
