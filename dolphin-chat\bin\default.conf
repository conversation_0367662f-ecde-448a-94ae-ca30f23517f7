server {
    listen 80;
    root /usr/share/nginx/html/;
    resolver *************** valid=30s;

    index index.html;

    location /api {
        set $backend http://**************:8080;
        proxy_pass $backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }
}