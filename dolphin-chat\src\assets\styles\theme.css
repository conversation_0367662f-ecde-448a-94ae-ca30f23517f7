/**
 * 全局主题样式
 * 统一管理应用的主题切换样式
 */

/* ===== CSS 变量定义 ===== */
:root {
  /* 浅色主题变量 */
  --app-bg-primary: #ffffff;
  --app-bg-secondary: #fafafa;
  --app-bg-tertiary: #f5f5f5;

  --app-text-primary: #1a1a1a;
  --app-text-secondary: #666666;
  --app-text-tertiary: #999999;

  --app-border-color: #e0e0e0;
  --app-border-light: #f0f0f0;

  --app-hover-bg: rgba(0, 0, 0, 0.04);
  --app-active-bg: rgba(25, 118, 210, 0.08);

  --app-shadow: rgba(0, 0, 0, 0.1);
  --app-shadow-light: rgba(0, 0, 0, 0.05);

  /* 图标颜色变量 - 统一灰色风格 */
  --icon-color-primary: #666666;
  --icon-color-secondary: #888888;
  --icon-color-tertiary: #aaaaaa;
  --icon-filter-primary: brightness(0) saturate(100%) invert(40%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
  --icon-filter-secondary: brightness(0) saturate(100%) invert(53%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
  --icon-filter-hover: brightness(0) saturate(100%) invert(20%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);

  /* 对话框变量 */
  --dialog-header-bg: #ffffff;
  --dialog-content-bg: #ffffff;
  --dialog-border-color: #e5e5e5;
  --dialog-title-color: #333333;
  --dialog-tab-bg: #f8f9fa;
  --dialog-tab-color: #666;
  --dialog-tab-hover-bg: rgba(25, 118, 210, 0.04);
  --dialog-tab-active-bg: #ffffff;
  --dialog-tab-active-color: #1976d2;
}

/* 深色主题变量 */
[data-theme="dark"] {
  --app-bg-primary: #1e1e1e;
  --app-bg-secondary: #121212;
  --app-bg-tertiary: #2d2d2d;

  --app-text-primary: #e0e0e0;
  --app-text-secondary: #b0b0b0;
  --app-text-tertiary: #808080;

  --app-border-color: #404040;
  --app-border-light: #2d2d2d;

  --app-hover-bg: rgba(255, 255, 255, 0.08);
  --app-active-bg: rgba(100, 181, 246, 0.12);

  --app-shadow: rgba(0, 0, 0, 0.3);
  --app-shadow-light: rgba(0, 0, 0, 0.2);

  /* 图标颜色变量 - 深色主题也使用统一灰色风格 */
  --icon-color-primary: #888888;
  --icon-color-secondary: #aaaaaa;
  --icon-color-tertiary: #cccccc;
  --icon-filter-primary: brightness(0) saturate(100%) invert(53%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
  --icon-filter-secondary: brightness(0) saturate(100%) invert(67%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
  --icon-filter-hover: brightness(0) saturate(100%) invert(40%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);

  /* 对话框变量 */
  --dialog-header-bg: #2d2d2d;
  --dialog-content-bg: #1e1e1e;
  --dialog-border-color: #404040;
  --dialog-title-color: #e0e0e0;
  --dialog-tab-bg: #2d2d2d;
  --dialog-tab-color: #b0b0b0;
  --dialog-tab-hover-bg: rgba(255, 255, 255, 0.08);
  --dialog-tab-active-bg: #1e1e1e;
  --dialog-tab-active-color: #64b5f6;
}

/* ===== 主题过渡动画 ===== */
* {
  transition: background-color 0.3s ease,
              color 0.3s ease,
              border-color 0.3s ease,
              box-shadow 0.3s ease;
}

/* ===== 应用级别的主题样式 ===== */
html, body {
  background-color: var(--app-bg-secondary) !important;
  color: var(--app-text-primary) !important;
}

.v-application {
  background-color: var(--app-bg-secondary) !important;
  color: var(--app-text-primary) !important;
}

/* 强制覆盖 Vuetify 的默认背景 */
.v-main {
  background-color: var(--app-bg-secondary) !important;
}

.v-app-bar {
  background-color: var(--app-bg-primary) !important;
  color: var(--app-text-primary) !important;
}

/* 确保所有容器都使用正确的背景色 */
#app {
  background-color: var(--app-bg-secondary) !important;
}

/* ===== 卡片组件主题 ===== */
.v-card {
  background-color: var(--app-bg-primary) !important;
  color: var(--app-text-primary) !important;
  border: 1px solid var(--app-border-color) !important;
}

.v-card-title {
  color: var(--app-text-primary) !important;
}

.v-card-text {
  color: var(--app-text-secondary) !important;
}

/* ===== 按钮组件主题 ===== */
.v-btn {
  transition: all 0.3s ease !important;
}

.v-btn--variant-text {
  color: var(--app-text-secondary) !important;
}

.v-btn--variant-text:hover {
  background-color: var(--app-hover-bg) !important;
}

/* ===== 输入框组件主题（排除登录页面） ===== */
.v-field:not(.login-page .v-field) {
  background-color: var(--app-bg-primary) !important;
  border-color: var(--app-border-color) !important;
}

.v-field--focused:not(.login-page .v-field--focused) {
  border-color: var(--v-theme-primary) !important;
}

.v-field__input:not(.login-page .v-field__input) {
  color: var(--app-text-primary) !important;
}

.v-field__input::placeholder:not(.login-page .v-field__input::placeholder) {
  color: var(--app-text-tertiary) !important;
}

.v-field__outline:not(.login-page .v-field__outline) {
  color: var(--app-border-color) !important;
}

.v-field__outline--focused:not(.login-page .v-field__outline--focused) {
  color: var(--v-theme-primary) !important;
}

/* ===== 选择器组件主题 ===== */
.v-select .v-field {
  background-color: var(--app-bg-secondary) !important;
}

.v-list {
  background-color: var(--app-bg-primary) !important;
  border: 1px solid var(--app-border-color) !important;
}

.v-list-item {
  color: var(--app-text-primary) !important;
}

.v-list-item:hover {
  background-color: var(--app-hover-bg) !important;
}

.v-list-item--active {
  background-color: var(--app-active-bg) !important;
}

/* ===== 对话框组件主题 ===== */
.v-dialog .v-card {
  box-shadow: 0 8px 32px var(--app-shadow) !important;
}

.v-overlay__scrim {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

[data-theme="dark"] .v-overlay__scrim {
  background-color: rgba(0, 0, 0, 0.7) !important;
}

/* ===== 导航组件主题 ===== */
.v-navigation-drawer {
  background-color: var(--app-bg-primary) !important;
  border-color: var(--app-border-color) !important;
}

/* ===== 工具栏组件主题 ===== */
.v-toolbar {
  background-color: var(--app-bg-primary) !important;
  color: var(--app-text-primary) !important;
  border-bottom: 1px solid var(--app-border-color) !important;
}

/* ===== 标签页组件主题 ===== */
.v-tabs {
  background-color: var(--app-bg-secondary) !important;
}

.v-tab {
  color: var(--app-text-secondary) !important;
}

.v-tab--selected {
  color: var(--v-theme-primary) !important;
}

.v-tab:hover {
  background-color: var(--app-hover-bg) !important;
}

/* ===== 分割线主题 ===== */
.v-divider {
  border-color: var(--app-border-color) !important;
}

/* ===== 图标主题 ===== */
.v-icon {
  color: inherit !important;
}

/* ===== 文本主题 ===== */
.text-primary {
  color: var(--app-text-primary) !important;
}

.text-secondary {
  color: var(--app-text-secondary) !important;
}

.text-tertiary {
  color: var(--app-text-tertiary) !important;
}

/* ===== 背景主题 ===== */
.bg-primary {
  background-color: var(--app-bg-primary) !important;
}

.bg-secondary {
  background-color: var(--app-bg-secondary) !important;
}

.bg-tertiary {
  background-color: var(--app-bg-tertiary) !important;
}

/* ===== 边框主题 ===== */
.border-color {
  border-color: var(--app-border-color) !important;
}

.border-light {
  border-color: var(--app-border-light) !important;
}

/* ===== 阴影主题 ===== */
.shadow {
  box-shadow: 0 2px 8px var(--app-shadow) !important;
}

.shadow-light {
  box-shadow: 0 1px 4px var(--app-shadow-light) !important;
}

/* ===== 悬停效果主题 ===== */
.hover-bg:hover {
  background-color: var(--app-hover-bg) !important;
}

.active-bg {
  background-color: var(--app-active-bg) !important;
}

/* ===== 特殊组件适配 ===== */
/* 消息气泡 */
.message-bubble {
  background-color: var(--app-bg-secondary);
  border: 1px solid var(--app-border-light);
  color: var(--app-text-primary);
}

.message-bubble.user {
  background-color: var(--v-theme-primary);
  color: white;
}

/* 代码块 */
.code-block {
  background-color: var(--app-bg-tertiary);
  border: 1px solid var(--app-border-color);
  color: var(--app-text-primary);
}

/* 侧边栏 */
.sidebar {
  background-color: var(--app-bg-primary);
  border-right: 1px solid var(--app-border-color);
}

/* 主内容区 */
.main-content {
  background-color: var(--app-bg-secondary);
}

/* ===== 强制覆盖一些顽固的样式 ===== */
/* 确保文本颜色正确（排除登录页面） */
.v-application .v-list-item-title:not(.login-page .v-list-item-title),
.v-application .v-list-item-subtitle:not(.login-page .v-list-item-subtitle),
.v-application .v-btn__content:not(.login-page .v-btn__content),
.v-application .v-field__input:not(.login-page .v-field__input),
.v-application .v-select__selection-text:not(.login-page .v-select__selection-text) {
  color: var(--app-text-primary) !important;
}

/* 确保背景颜色正确 */
.v-application .v-list,
.v-application .v-menu .v-list,
.v-application .v-select__menu .v-list {
  background-color: var(--app-bg-primary) !important;
}

/* 确保边框颜色正确（排除登录页面） */
.v-application .v-field__outline__start:not(.login-page .v-field__outline__start),
.v-application .v-field__outline__notch:not(.login-page .v-field__outline__notch),
.v-application .v-field__outline__end:not(.login-page .v-field__outline__end) {
  border-color: var(--app-border-color) !important;
}

/* 欢迎页面样式 */
.welcome-container {
  background-color: var(--app-bg-secondary) !important;
  color: var(--app-text-primary) !important;
}

/* 聊天消息容器 */
.chat-messages {
  background-color: var(--app-bg-secondary) !important;
}

/* ===== 强制覆盖顽固的白色背景 ===== */
/* 侧边栏相关 */
.v-navigation-drawer,
.v-navigation-drawer .v-navigation-drawer__content {
  background-color: var(--app-bg-primary) !important;
}

/* 对话历史相关 */
.conversation-history,
.conversation-list {
  background-color: var(--app-bg-primary) !important;
}

/* 输入框相关（排除登录页面） */
.v-textarea:not(.login-page .v-textarea),
.v-textarea .v-field:not(.login-page .v-field),
.v-text-field:not(.login-page .v-text-field),
.v-text-field .v-field:not(.login-page .v-field) {
  background-color: var(--app-bg-primary) !important;
}

/* 容器相关 */
.v-container {
  background-color: transparent !important;
}

/* 确保所有可能的白色背景都被覆盖 */
[style*="background-color: white"],
[style*="background-color: #ffffff"],
[style*="background-color: #fff"],
[style*="background: white"],
[style*="background: #ffffff"],
[style*="background: #fff"] {
  background-color: var(--app-bg-primary) !important;
  background: var(--app-bg-primary) !important;
}

/* 特定的深色主题强制样式 */
[data-theme="dark"] .v-application,
[data-theme="dark"] .v-main,
[data-theme="dark"] .v-navigation-drawer,
[data-theme="dark"] .conversation-history,
[data-theme="dark"] .conversation-list,
[data-theme="dark"] .sidebar-content,
[data-theme="dark"] .brand-section,
[data-theme="dark"] .bottom-menu {
  background-color: var(--app-bg-primary) !important;
}

/* ===== 按钮主题强制覆盖 ===== */
/* 确保所有按钮都使用正确的主题色，但排除登录页面的按钮 */
.v-btn:not(.feature-btn--active):not(.send-btn):not(.stop-btn):not(.login-btn) {
  background-color: var(--app-bg-primary) !important;
  color: var(--app-text-primary) !important;
}

.v-btn:not(.feature-btn--active):not(.send-btn):not(.stop-btn):not(.login-btn):hover {
  background-color: var(--app-hover-bg) !important;
}

/* 输入框按钮特殊处理 */
.feature-btn:not(.feature-btn--active) {
  background-color: var(--app-bg-primary) !important;
  color: var(--app-text-primary) !important;
  border-color: var(--app-border-color) !important;
}

.action-btn {
  background-color: var(--app-bg-primary) !important;
  color: var(--app-text-secondary) !important;
}

/* 深色主题下的按钮强制样式 */
[data-theme="dark"] .v-btn:not(.feature-btn--active):not(.send-btn):not(.stop-btn):not(.login-btn) {
  background-color: var(--app-bg-secondary) !important;
  color: var(--app-text-primary) !important;
}

[data-theme="dark"] .feature-btn:not(.feature-btn--active) {
  background-color: var(--app-bg-secondary) !important;
  color: var(--app-text-primary) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

[data-theme="dark"] .action-btn {
  background-color: var(--app-bg-secondary) !important;
  color: var(--app-text-secondary) !important;
}

/* ===== 模型选择器主题强制覆盖 ===== */
.model-selector-btn {
  background-color: var(--app-bg-primary) !important;
  color: var(--app-text-primary) !important;
  border-color: var(--app-border-color) !important;
}

.model-dropdown {
  background-color: var(--app-bg-primary) !important;
  border-color: var(--app-border-color) !important;
}

.model-name,
.model-item-name {
  color: var(--app-text-primary) !important;
}

.model-desc,
.model-item-desc,
.dropdown-title {
  color: var(--app-text-secondary) !important;
}

/* 深色主题下的模型选择器强制样式 */
[data-theme="dark"] .model-selector-btn {
  background-color: var(--app-bg-secondary) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

[data-theme="dark"] .model-dropdown {
  background-color: var(--app-bg-secondary) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

/* ===== 对话框内容主题强制覆盖 ===== */
/* 确保对话框内容区域使用正确的背景色 */
.v-dialog .v-card,
.deepseek-settings-dialog,
.deepseek-content {
  background-color: var(--dialog-content-bg) !important;
  color: var(--app-text-primary) !important;
}

/* 设置标签页内容 */
.tab-panel {
  background-color: transparent !important;
  color: var(--app-text-primary) !important;
}

/* 设置项样式 */
.setting-row,
.info-row,
.service-section,
.account-section {
  background-color: transparent !important;
}

/* 文本颜色强制覆盖 */
.section-title,
.setting-label,
.info-label,
.info-value {
  color: var(--app-text-primary) !important;
}

.setting-description,
.agreement-content,
.loading-container {
  color: var(--app-text-secondary) !important;
}

/* 深色主题下的对话框强制样式 */
[data-theme="dark"] .v-dialog .v-card,
[data-theme="dark"] .deepseek-settings-dialog,
[data-theme="dark"] .deepseek-content {
  background-color: var(--app-bg-primary) !important;
  color: var(--app-text-primary) !important;
}

/* ===== 系统设置对话框强制样式 ===== */
.deepseek-settings-dialog {
  background-color: var(--app-bg-primary) !important;
}

.deepseek-header {
  background-color: var(--app-bg-primary) !important;
  border-color: var(--app-border-color) !important;
}

.deepseek-content {
  background-color: var(--app-bg-primary) !important;
}

.deepseek-tabs {
  background-color: var(--app-bg-secondary) !important;
  border-color: var(--app-border-color) !important;
}

.deepseek-title {
  color: var(--app-text-primary) !important;
}

.deepseek-tab {
  color: var(--app-text-secondary) !important;
}

.deepseek-tab.active {
  color: var(--v-theme-primary) !important;
  background-color: var(--app-bg-primary) !important;
  border-bottom-color: var(--v-theme-primary) !important;
}

/* 深色主题下的设置对话框特殊处理 */
[data-theme="dark"] .deepseek-settings-dialog,
[data-theme="dark"] .deepseek-header,
[data-theme="dark"] .deepseek-content {
  background-color: var(--app-bg-primary) !important;
}

[data-theme="dark"] .deepseek-tabs {
  background-color: var(--app-bg-secondary) !important;
}

/* ===== 响应式主题适配 ===== */
@media (max-width: 768px) {
  /* 移动端主题适配 */
  .v-dialog {
    margin: 16px !important;
  }

  .v-card {
    border-radius: 12px !important;
  }
}

/* ===== 主题切换动画 ===== */
@keyframes theme-transition {
  0% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}

.theme-transition {
  animation: theme-transition 0.3s ease;
}

/* ===== 无障碍支持 ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}

/* ===== 登录页面样式隔离 ===== */
/* 确保登录页面不受全局主题影响，保持原有的高质量外观 */
.login-page {
  /* 重置所有可能的全局样式影响 */
  background: unset !important;
  color: unset !important;
}

.login-page * {
  /* 禁用全局主题过渡动画，避免登录页面样式闪烁 */
  transition: none !important;
}

.login-page .v-btn.login-btn {
  /* 确保登录按钮样式不被覆盖 */
  background: #4a90e2 !important;
  color: white !important;
  border-radius: 8px !important;
  height: 48px !important;
  font-size: 1rem !important;
  font-weight: 500 !important;
  text-transform: none !important;
  box-shadow: 0 2px 4px rgba(74, 144, 226, 0.2) !important;
}

.login-page .v-btn.login-btn:hover {
  background: #357abd !important;
  box-shadow: 0 4px 8px rgba(74, 144, 226, 0.3) !important;
}

.login-page .v-btn.login-btn:disabled {
  background: rgba(74, 144, 226, 0.5) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  box-shadow: none !important;
}

/* 确保登录页面的其他元素也不受全局主题影响 */
.login-page .v-field,
.login-page .v-field__input,
.login-page .v-field__outline,
.login-page .v-field__outline__start,
.login-page .v-field__outline__notch,
.login-page .v-field__outline__end,
.login-page .v-text-field,
.login-page .v-text-field .v-field,
.login-page .v-checkbox,
.login-page .v-alert {
  /* 保持登录页面组件的原始样式 */
  background: unset !important;
  color: unset !important;
  border-color: unset !important;
}

/* 强制重置登录页面的输入框样式 */
.login-page .v-field {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.9) !important;
  border-radius: 8px !important;
}

.login-page .v-field--focused {
  background-color: rgba(255, 255, 255, 0.08) !important;
  border-color: #4a90e2 !important;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2) !important;
}

.login-page .v-field__input {
  color: white !important;
}

.login-page .v-label {
  color: rgba(255, 255, 255, 0.7) !important;
}

.login-page .v-icon {
  color: rgba(255, 255, 255, 0.7) !important;
}

/* 登录页面输入框轮廓线样式 */
.login-page .v-field__outline__start,
.login-page .v-field__outline__notch,
.login-page .v-field__outline__end {
  border-color: rgba(255, 255, 255, 0.3) !important;
}

.login-page .v-field--focused .v-field__outline__start,
.login-page .v-field--focused .v-field__outline__notch,
.login-page .v-field--focused .v-field__outline__end {
  border-color: #4a90e2 !important;
  border-width: 2px !important;
}

/* ===== 图标主题适配 - 统一灰色风格 ===== */
/* 通用图标样式 - 在所有主题下都使用统一的灰色 */
.toggle-icon,
.new-chat-icon,
.new-chat-icon-small,
.app-icon,
.app-icon-small,
.profile-icon,
.action-icon {
  filter: var(--icon-filter-primary);
  transition: filter 0.3s ease;
}

/* 功能卡片图标特殊处理 - 浅色主题 */
.feature-icon {
  filter: brightness(0.9) contrast(1.2) saturate(1.1);
  transition: filter 0.3s ease, transform 0.3s ease;
  opacity: 1;
  mix-blend-mode: normal;
}

/* 图标hover状态 - 统一的hover效果 */
.toggle-btn:hover .toggle-icon,
.new-chat-button:hover .new-chat-icon,
.new-chat-icon-btn:hover .new-chat-icon-small,
.bottom-menu .v-list-item:hover .app-icon,
.app-icon-btn:hover .app-icon-small,
.profile-menu:hover .profile-icon,
.action-btn:hover .action-icon {
  filter: var(--icon-filter-hover);
}

/* 功能卡片图标hover效果 */
.feature-card--clickable:hover .feature-icon {
  filter: brightness(0.7) contrast(1.4) saturate(1.2);
  transform: scale(1.05);
}

/* 深色主题下保持相同的灰色风格 */
[data-theme="dark"] .toggle-icon,
[data-theme="dark"] .new-chat-icon,
[data-theme="dark"] .new-chat-icon-small,
[data-theme="dark"] .app-icon,
[data-theme="dark"] .app-icon-small,
[data-theme="dark"] .profile-icon,
[data-theme="dark"] .action-icon {
  filter: var(--icon-filter-primary);
}

/* 深色主题下的功能卡片图标 - 保持原始颜色 */
[data-theme="dark"] .feature-icon {
  filter: none;
  opacity: 1;
  mix-blend-mode: normal;
}

[data-theme="dark"] .toggle-btn:hover .toggle-icon,
[data-theme="dark"] .new-chat-button:hover .new-chat-icon,
[data-theme="dark"] .new-chat-icon-btn:hover .new-chat-icon-small,
[data-theme="dark"] .bottom-menu .v-list-item:hover .app-icon,
[data-theme="dark"] .app-icon-btn:hover .app-icon-small,
[data-theme="dark"] .profile-menu:hover .profile-icon,
[data-theme="dark"] .action-btn:hover .action-icon {
  filter: var(--icon-filter-hover);
}

/* 深色主题下的功能卡片图标hover效果 */
[data-theme="dark"] .feature-card--clickable:hover .feature-icon {
  filter: brightness(1.5) contrast(1.3) saturate(1.1) invert(0.1);
  transform: scale(1.05);
}

/* ===== 高对比度模式支持 ===== */
@media (prefers-contrast: high) {
  :root {
    --app-border-color: #000000;
    --app-text-secondary: #000000;
    /* 高对比度模式下使用更深的灰色 */
    --icon-filter-primary: brightness(0) saturate(100%) invert(20%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
    --icon-filter-hover: brightness(0) saturate(100%) invert(10%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
  }

  /* 高对比度模式下的功能卡片图标 */
  .feature-icon {
    filter: brightness(0.6) contrast(1.5) saturate(0.5) !important;
  }

  [data-theme="dark"] {
    --app-border-color: #ffffff;
    --app-text-secondary: #ffffff;
    /* 深色主题高对比度模式下使用更浅的灰色 */
    --icon-filter-primary: brightness(0) saturate(100%) invert(80%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
    --icon-filter-hover: brightness(0) saturate(100%) invert(60%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
  }

  /* 深色主题高对比度模式下的功能卡片图标 */
  [data-theme="dark"] .feature-icon {
    filter: brightness(1.6) contrast(1.4) saturate(0.8) invert(0.2) !important;
  }
}
