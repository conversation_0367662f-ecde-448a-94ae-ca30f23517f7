<script setup>
import { ref } from 'vue'
import FeatureCard from './featurecard.vue'

// 导入SVG图标
import DataIcon from '@/assets/icons/Data.svg'
import DiagnosticIcon from '@/assets/icons/Diagnostic.svg'
import GynecologicalIcon from '@/assets/icons/Gynecological.svg'
import HurtIcon from '@/assets/icons/hurt.svg'
import LearningIcon from '@/assets/icons/Learning.svg'
import ResourcesIcon from '@/assets/icons/resources.svg'

// 功能卡片数据
const features = ref([
  {
    id: 1,
    icon: DiagnosticIcon,
    title: '医学咨询',
  },
  {
    id: 2,
    icon: HurtIcon,
    title: '心脏超声',
  },
  {
    id: 3,
    icon: GynecologicalIcon,
    title: '妇科检查',
  },
  {
    id: 4,
    icon: ResourcesIcon,
    title: '诊断建议',
  },
  {
    id: 5,
    icon: DataIcon,
    title: '数据分析',
  },
  {
    id: 6,
    icon: LearningIcon,
    title: '学习资源',
  }
])

// Emits
const emit = defineEmits(['feature-click'])

// 方法
const handleFeatureClick = (feature) => {
  console.log('点击功能卡片:', feature.title)
  emit('feature-click', feature)
}
</script>

<template>
  <div class="feature-cards">
    <VContainer>
      <VRow>
        <VCol
          v-for="feature in features"
          :key="feature.id"
          cols="6"
          sm="4"
          md="2"
        >
          <FeatureCard
            :icon="feature.icon"
            :title="feature.title"
            @click="handleFeatureClick(feature)"
          />
        </VCol>
      </VRow>
    </VContainer>
  </div>
</template>

<style scoped>
.feature-cards {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .feature-cards {
    padding: 0 8px;
  }
}
</style>
