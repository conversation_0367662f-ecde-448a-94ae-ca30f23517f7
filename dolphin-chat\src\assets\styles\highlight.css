/* highlight.js 代码高亮样式 - GitHub 主题 */
.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  color: #333;
  background: #f8f8f8;
}

.hljs-comment,
.hljs-quote {
  color: #998;
  font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-subst {
  color: #333;
  font-weight: bold;
}

.hljs-number,
.hljs-literal,
.hljs-variable,
.hljs-template-variable,
.hljs-tag .hljs-attr {
  color: #008080;
}

.hljs-string,
.hljs-doctag {
  color: #d14;
}

.hljs-title,
.hljs-section,
.hljs-selector-id {
  color: #900;
  font-weight: bold;
}

.hljs-subst {
  font-weight: normal;
}

.hljs-type,
.hljs-class .hljs-title {
  color: #458;
  font-weight: bold;
}

.hljs-tag,
.hljs-name,
.hljs-attribute {
  color: #000080;
  font-weight: normal;
}

.hljs-regexp,
.hljs-link {
  color: #009926;
}

/* ===== Markdown 样式 ===== */
/* 重置和基础样式 */
.text-rendered,
.markdown-rendered {
  color: #1f2937 !important;
  line-height: 1.6 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

/* 标题样式 */
.text-rendered h1,
.markdown-rendered h1 {
  font-size: 1.875rem !important; /* 30px */
  font-weight: 700 !important;
  margin: 1.5rem 0 1rem 0 !important;
  color: #111827 !important;
  border-bottom: 2px solid #e5e7eb !important;
  padding-bottom: 0.5rem !important;
}

.text-rendered h2,
.markdown-rendered h2 {
  font-size: 1.5rem !important; /* 24px */
  font-weight: 600 !important;
  margin: 1.25rem 0 0.75rem 0 !important;
  color: #111827 !important;
  border-bottom: 1px solid #e5e7eb !important;
  padding-bottom: 0.25rem !important;
}

.text-rendered h3,
.markdown-rendered h3 {
  font-size: 1.25rem !important; /* 20px */
  font-weight: 600 !important;
  margin: 1rem 0 0.5rem 0 !important;
  color: #111827 !important;
}

.text-rendered h4,
.markdown-rendered h4 {
  font-size: 1.125rem !important; /* 18px */
  font-weight: 600 !important;
  margin: 0.875rem 0 0.5rem 0 !important;
  color: #111827 !important;
}

.text-rendered h5,
.markdown-rendered h5 {
  font-size: 1rem !important; /* 16px */
  font-weight: 600 !important;
  margin: 0.75rem 0 0.5rem 0 !important;
  color: #111827 !important;
}

.text-rendered h6,
.markdown-rendered h6 {
  font-size: 0.875rem !important; /* 14px */
  font-weight: 600 !important;
  margin: 0.75rem 0 0.5rem 0 !important;
  color: #6b7280 !important;
}

/* 段落样式 */
.text-rendered p,
.markdown-rendered p {
  margin: 0.75rem 0 !important;
  color: #374151 !important;
  font-size: 0.9rem !important;
  line-height: 1.6 !important;
}

/* 列表样式 */
.text-rendered ul,
.markdown-rendered ul,
.text-rendered ol,
.markdown-rendered ol {
  margin: 0.75rem 0 !important;
  padding-left: 1.5rem !important;
}

.text-rendered li,
.markdown-rendered li {
  margin: 0.25rem 0 !important;
  color: #374151 !important;
  font-size: 0.9rem !important;
  line-height: 1.5 !important;
}

.text-rendered ul li,
.markdown-rendered ul li {
  list-style-type: disc !important;
}

.text-rendered ol li,
.markdown-rendered ol li {
  list-style-type: decimal !important;
}

/* 嵌套列表 */
.text-rendered ul ul li,
.markdown-rendered ul ul li {
  list-style-type: circle !important;
}

.text-rendered ul ul ul li,
.markdown-rendered ul ul ul li {
  list-style-type: square !important;
}

/* 强调样式 */
.text-rendered strong,
.markdown-rendered strong,
.text-rendered b,
.markdown-rendered b {
  font-weight: 700 !important;
  color: #111827 !important;
}

.text-rendered em,
.markdown-rendered em,
.text-rendered i,
.markdown-rendered i {
  font-style: italic !important;
  color: #374151 !important;
}

/* 代码样式 */
.text-rendered code,
.markdown-rendered code {
  background-color: #f3f4f6 !important;
  color: #dc2626 !important;
  padding: 0.125rem 0.25rem !important;
  border-radius: 0.25rem !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 0.875rem !important;
}

.text-rendered pre,
.markdown-rendered pre {
  background-color: #f8f9fa !important;
  border: 1px solid #e9ecef !important;
  border-radius: 0.375rem !important;
  padding: 1rem !important;
  margin: 1rem 0 !important;
  overflow-x: auto !important;
}

.text-rendered pre code,
.markdown-rendered pre code {
  background-color: transparent !important;
  color: inherit !important;
  padding: 0 !important;
  border-radius: 0 !important;
}

/* 引用样式 */
.text-rendered blockquote,
.markdown-rendered blockquote {
  border-left: 4px solid #d1d5db !important;
  margin: 1rem 0 !important;
  padding: 0.5rem 0 0.5rem 1rem !important;
  background-color: #f9fafb !important;
  color: #6b7280 !important;
  font-style: italic !important;
}

.text-rendered blockquote p,
.markdown-rendered blockquote p {
  margin: 0.5rem 0 !important;
}

/* 链接样式 */
.text-rendered a,
.markdown-rendered a {
  color: #2563eb !important;
  text-decoration: underline !important;
}

.text-rendered a:hover,
.markdown-rendered a:hover {
  color: #1d4ed8 !important;
}

/* 表格样式 */
.text-rendered table,
.markdown-rendered table {
  border-collapse: collapse !important;
  width: 100% !important;
  margin: 1rem 0 !important;
  border: 1px solid #e5e7eb !important;
}

.text-rendered th,
.markdown-rendered th,
.text-rendered td,
.markdown-rendered td {
  border: 1px solid #e5e7eb !important;
  padding: 0.5rem !important;
  text-align: left !important;
}

.text-rendered th,
.markdown-rendered th {
  background-color: #f9fafb !important;
  font-weight: 600 !important;
}

/* 分隔线样式 */
.text-rendered hr,
.markdown-rendered hr {
  border: none !important;
  border-top: 1px solid #e5e7eb !important;
  margin: 1.5rem 0 !important;
}

.hljs-symbol,
.hljs-bullet {
  color: #990073;
}

.hljs-built_in,
.hljs-builtin-name {
  color: #0086b3;
}

.hljs-meta {
  color: #999;
  font-weight: bold;
}

.hljs-deletion {
  background: #fdd;
}

.hljs-addition {
  background: #dfd;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}

/* 暗色主题代码块样式 */
.markdown-rendered pre .hljs,
.text-rendered pre .hljs {
  background: #1f2937;
  color: #f9fafb;
}

.markdown-rendered pre .hljs-comment,
.text-rendered pre .hljs-comment,
.markdown-rendered pre .hljs-quote,
.text-rendered pre .hljs-quote {
  color: #6b7280;
}

.markdown-rendered pre .hljs-keyword,
.text-rendered pre .hljs-keyword,
.markdown-rendered pre .hljs-selector-tag,
.text-rendered pre .hljs-selector-tag {
  color: #f59e0b;
}

.markdown-rendered pre .hljs-string,
.text-rendered pre .hljs-string,
.markdown-rendered pre .hljs-doctag,
.text-rendered pre .hljs-doctag {
  color: #10b981;
}

.markdown-rendered pre .hljs-number,
.text-rendered pre .hljs-number,
.markdown-rendered pre .hljs-literal,
.text-rendered pre .hljs-literal {
  color: #f472b6;
}

.markdown-rendered pre .hljs-title,
.text-rendered pre .hljs-title,
.markdown-rendered pre .hljs-section,
.text-rendered pre .hljs-section {
  color: #60a5fa;
}

.markdown-rendered pre .hljs-type,
.text-rendered pre .hljs-type,
.markdown-rendered pre .hljs-class .hljs-title,
.text-rendered pre .hljs-class .hljs-title {
  color: #a78bfa;
}

.markdown-rendered pre .hljs-built_in,
.text-rendered pre .hljs-built_in,
.markdown-rendered pre .hljs-builtin-name,
.text-rendered pre .hljs-builtin-name {
  color: #06b6d4;
}

.markdown-rendered pre .hljs-meta,
.text-rendered pre .hljs-meta {
  color: #9ca3af;
}

.markdown-rendered pre .hljs-tag,
.text-rendered pre .hljs-tag,
.markdown-rendered pre .hljs-name,
.text-rendered pre .hljs-name,
.markdown-rendered pre .hljs-attribute,
.text-rendered pre .hljs-attribute {
  color: #fbbf24;
}

.markdown-rendered pre .hljs-regexp,
.text-rendered pre .hljs-regexp,
.markdown-rendered pre .hljs-link,
.text-rendered pre .hljs-link {
  color: #34d399;
}
