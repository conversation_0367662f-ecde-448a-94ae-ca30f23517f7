<template>
  <div class="message-footer">
    <!-- AI消息的操作按钮 -->
    <div v-if="messageType === 'ai'" class="message-actions">
      <VBtn icon="mdi-content-copy" size="x-small" variant="text" @click="handleCopyMessage" />
      <VBtn icon="mdi-refresh" size="x-small" variant="text" @click="handleRegenerateMessage" />
      <!-- 显示询问图片到画布的按钮 -->
      <VBtn
        v-if="hasUserImages"
        icon="mdi-image-multiple-outline"
        size="x-small"
        variant="text"
        @click="handleShowUserImages"
        :title="'在右侧画布中显示询问的图片'" />
    </div>

    <!-- 图片消息的操作按钮 -->
    <div v-if="messageType === 'image'" class="message-actions">
      <VBtn
        icon="mdi-download-outline"
        size="x-small"
        variant="text"
        @click="handleDownloadImage" />
      <VBtn
        icon="mdi-fullscreen-exit"
        size="x-small"
        variant="text"
        @click="handleOpenImagePreview" />
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue"

// 接收props
const props = defineProps({
  messageType: {
    type: String,
    required: true,
  },
  content: {
    type: String,
    default: "",
  },
  imageUrl: {
    type: String,
    default: "",
  },
  imageName: {
    type: String,
    default: "",
  },
  // 新增：用户询问的图片信息
  userImages: {
    type: Array,
    default: () => [],
  },
})

// 定义emits
const emit = defineEmits(["download-image", "open-image-preview", "show-user-images"])

// 计算属性：检查是否有用户询问的图片
const hasUserImages = computed(() => {
  return props.messageType === "ai" && props.userImages && props.userImages.length > 0
})

// 事件处理
const handleCopyMessage = () => {
  navigator.clipboard.writeText(props.content)
  console.log("消息已复制")
}

const handleRegenerateMessage = () => {
  console.log("重新生成消息")
}

const handleDownloadImage = () => {
  emit("download-image", props.imageUrl, props.imageName)
}

const handleOpenImagePreview = () => {
  emit("open-image-preview")
}

// 新增：处理显示用户询问图片到画布
const handleShowUserImages = () => {
  emit("show-user-images", props.userImages)
}
</script>

<style scoped>
.message-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 8px;
  padding-top: 4px;
}

.message-actions {
  display: flex;
  gap: 6px;
  opacity: 0;
  transition: all 0.3s ease;
  transform: translateX(10px);
}

.message-container:hover .message-actions {
  opacity: 1;
  transform: translateX(0);
}

.message-actions .v-btn {
  background: transparent;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  min-width: 24px;
  color: rgba(0, 0, 0, 0.6);
  transition: all 0.2s ease;
}

.message-actions .v-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.8);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-actions {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
