import { defineStore } from "pinia"
import { ModelService } from '@/api/model'

export const useAppStore = defineStore("app", {
  state: () => ({
    // 应用全局状态
    loading: false,
    // 主题相关状态
    theme: 'system', // 'light' | 'dark' | 'system'
    actualTheme: 'light', // 实际应用的主题 'light' | 'dark'
    systemTheme: 'light', // 系统主题 'light' | 'dark'
  }),

  getters: {
    // 获取当前应用的主题
    currentTheme: (state) => {
      return state.theme === 'system' ? state.systemTheme : state.theme
    },

    // 获取主题显示名称
    themeDisplayName: (state) => {
      const themeNames = {
        'system': '跟随系统',
        'light': '浅色主题',
        'dark': '深色主题'
      }
      return themeNames[state.theme] || '跟随系统'
    }
  },

  actions: {
    setLoading(loading) {
      this.loading = loading
    },

    // 设置主题
    setTheme(theme) {
      this.theme = theme
      this.updateActualTheme()
      this.saveThemeToStorage()
    },

    // 切换主题（保持原有的 toggleTheme 方法兼容性）
    toggleTheme() {
      const themes = ['light', 'dark', 'system']
      const currentIndex = themes.indexOf(this.theme)
      const nextIndex = (currentIndex + 1) % themes.length
      this.setTheme(themes[nextIndex])
    },

    // 设置系统主题
    setSystemTheme(systemTheme) {
      this.systemTheme = systemTheme
      this.updateActualTheme()
    },

    // 更新实际应用的主题
    updateActualTheme() {
      this.actualTheme = this.theme === 'system' ? this.systemTheme : this.theme
    },

    // 从本地存储加载主题
    loadThemeFromStorage() {
      const savedTheme = localStorage.getItem('dolphin-theme')
      if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
        this.theme = savedTheme
      }
      this.updateActualTheme()
    },

    // 保存主题到本地存储
    saveThemeToStorage() {
      localStorage.setItem('dolphin-theme', this.theme)
    },

    // 初始化主题系统
    initTheme() {
      // 检测系统主题
      this.detectSystemTheme()

      // 加载用户保存的主题偏好
      this.loadThemeFromStorage()

      // 监听系统主题变化
      this.watchSystemTheme()
    },

    // 检测系统主题
    detectSystemTheme() {
      if (typeof window !== 'undefined' && window.matchMedia) {
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)')
        this.systemTheme = prefersDark.matches ? 'dark' : 'light'
      }
    },

    // 监听系统主题变化
    watchSystemTheme() {
      if (typeof window !== 'undefined' && window.matchMedia) {
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)')

        const handleThemeChange = (e) => {
          this.setSystemTheme(e.matches ? 'dark' : 'light')
        }

        // 现代浏览器使用 addEventListener
        if (prefersDark.addEventListener) {
          prefersDark.addEventListener('change', handleThemeChange)
        } else {
          // 兼容旧版浏览器
          prefersDark.addListener(handleThemeChange)
        }
      }
    }
  },
})

// 聊天状态管理
export const useChatStore = defineStore("chat", {
  state: () => ({
    // 聊天相关状态
    messages: [],
    currentConversationId: null,
    isLoading: false,
    showWelcome: true,
    // 照片墙相关状态
    showPhotoWall: false,
    photoWallImages: [], // 存储所有图片消息的引用
    // 图片画布相关状态
    showImageCanvas: false,
    canvasImages: [], // 存储画布中显示的图片
    // API相关状态
    apiError: null,
    connectionStatus: 'connected', // connected, disconnected, connecting
    // 流式响应状态
    streamingMessageId: null, // 当前正在流式更新的消息ID
    isStreamingActive: false, // 是否有活跃的流式响应
    // 模型选择相关状态
    selectedModel: 'gpt-4',
    availableModels: [
      {
        id: 'gpt-4',
        name: 'GPT-4',
        description: 'GPT-4 模型',
        icon: 'mdi-brain',
        color: 'primary'
      },
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        description: 'GPT-3.5 Turbo 模型',
        icon: 'mdi-lightning-bolt',
        color: 'success'
      },
      {
        id: 'claude-3',
        name: 'Claude-3',
        description: 'Claude-3 模型',
        icon: 'mdi-book-open-variant',
        color: 'info'
      },
      {
        id: 'gemini-pro',
        name: 'Gemini Pro',
        description: 'Gemini Pro 模型',
        icon: 'mdi-star',
        color: 'warning'
      }
    ],
    // 模型加载状态
    modelsLoading: false,
    modelsError: null
  }),

  getters: {
    // 获取当前对话的消息
    currentMessages: (state) => {
      return state.messages.filter(msg =>
        msg.conversationId === state.currentConversationId
      )
    },

    // 检查是否有消息
    hasMessages: (state) => {
      return state.messages.length > 0
    },

    // 获取所有图片消息
    imageMessages: (state) => {
      return state.messages.filter(msg => msg.type === 'image')
    },

    // 获取当前对话的图片消息
    currentImageMessages: (state) => {
      return state.messages.filter(msg =>
        msg.conversationId === state.currentConversationId && msg.type === 'image'
      )
    }
  },

  actions: {
    // 添加用户消息
    addUserMessage(content) {
      const message = {
        id: Date.now(),
        type: 'user',
        content: content.trim(),
        timestamp: new Date(),
        conversationId: this.currentConversationId || this.createNewConversation()
      }

      this.messages.push(message)
      this.showWelcome = false

      return message
    },

    // 添加图片消息
    addImageMessage(imageData, caption = '') {
      const message = {
        id: Date.now(),
        type: 'image',
        content: caption,
        imageUrl: imageData.url,
        imageFile: imageData.file,
        imageName: imageData.name,
        timestamp: new Date(),
        conversationId: this.currentConversationId || this.createNewConversation()
      }

      this.messages.push(message)
      this.showWelcome = false

      // 更新照片墙数据
      this.updatePhotoWall()

      return message
    },

    // 添加多图片消息
    addMultiImageMessage(imagesData, caption = '') {
      const message = {
        id: Date.now(),
        type: 'multi-image',
        content: caption,
        images: imagesData.map(imageData => ({
          url: imageData.url,
          file: imageData.file,
          name: imageData.name,
          id: imageData.id
        })),
        timestamp: new Date(),
        conversationId: this.currentConversationId || this.createNewConversation()
      }

      this.messages.push(message)
      this.showWelcome = false

      // 更新照片墙数据
      this.updatePhotoWall()

      return message
    },

    // 添加AI回复
    addAIMessage(content) {
      const message = {
        id: Date.now() + 1,
        type: 'ai',
        content: content,
        timestamp: new Date(),
        conversationId: this.currentConversationId,
        isStreaming: false // 标记是否为流式消息
      }

      this.messages.push(message)
      return message
    },

    // 更新AI消息内容（用于流式更新）
    updateAIMessage(messageId, content) {
      const messageIndex = this.messages.findIndex(msg => msg.id === messageId)
      if (messageIndex !== -1) {
        this.messages[messageIndex].content = content
        this.messages[messageIndex].timestamp = new Date()
        this.messages[messageIndex].isStreaming = true
      }
    },

    // 完成流式消息更新
    completeStreamingMessage(messageId) {
      const messageIndex = this.messages.findIndex(msg => msg.id === messageId)
      if (messageIndex !== -1) {
        this.messages[messageIndex].isStreaming = false
      }
      this.streamingMessageId = null
      this.isStreamingActive = false
    },

    // 开始流式消息
    startStreamingMessage(messageId) {
      this.streamingMessageId = messageId
      this.isStreamingActive = true
    },

    // 创建新对话
    createNewConversation() {
      const conversationId = 'conv_' + Date.now()
      this.currentConversationId = conversationId
      this.showWelcome = true
      this.isLoading = false
      return conversationId
    },

    // 设置加载状态
    setLoading(loading) {
      this.isLoading = loading
    },

    // 切换欢迎页面显示
    setShowWelcome(show) {
      this.showWelcome = show
    },

    // 清空当前对话
    clearCurrentConversation() {
      if (this.currentConversationId) {
        this.messages = this.messages.filter(msg =>
          msg.conversationId !== this.currentConversationId
        )
      }
      this.showWelcome = true
      this.updatePhotoWall()
    },

    // 切换照片墙显示状态
    togglePhotoWall() {
      this.showPhotoWall = !this.showPhotoWall
    },

    // 图片画布相关方法
    showImageCanvasPanel() {
      this.showImageCanvas = true
    },

    hideImageCanvas() {
      this.showImageCanvas = false
    },

    toggleImageCanvas() {
      this.showImageCanvas = !this.showImageCanvas
    },

    // 设置画布图片
    setCanvasImages(images) {
      this.canvasImages = images || []
    },

    // 添加图片到画布
    addImagesToCanvas(images) {
      if (!images || images.length === 0) return

      // 清空现有画布内容，显示新的图片
      this.canvasImages = images.map(image => ({
        id: `canvas_${Date.now()}_${Math.random()}`,
        url: image.url,
        name: image.name,
        originalId: image.id,
        timestamp: new Date()
      }))

      // 显示画布
      this.showImageCanvas = true

      // 触发自定义事件来收缩侧边栏
      window.dispatchEvent(new CustomEvent('collapse-sidebar'))
    },

    // 更新照片墙数据
    updatePhotoWall() {
      const imageMessages = []

      this.messages.forEach(msg => {
        if (msg.type === 'image') {
          // 单张图片消息
          imageMessages.push({
            id: msg.id,
            url: msg.imageUrl,
            name: msg.imageName,
            timestamp: msg.timestamp,
            conversationId: msg.conversationId,
            messageId: msg.id
          })
        } else if (msg.type === 'multi-image' && msg.images) {
          // 多张图片消息，为每张图片创建一个条目
          msg.images.forEach((image, index) => {
            imageMessages.push({
              id: `${msg.id}_${index}`,
              url: image.url,
              name: image.name,
              timestamp: msg.timestamp,
              conversationId: msg.conversationId,
              messageId: msg.id
            })
          })
        }
      })

      this.photoWallImages = imageMessages
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
    },

    // 定位到指定消息
    scrollToMessage(messageId) {
      // 这个方法会被组件调用来滚动到指定消息
      const message = this.messages.find(msg => msg.id === messageId)
      if (message) {
        // 如果消息不在当前对话中，切换到对应对话
        if (message.conversationId !== this.currentConversationId) {
          this.currentConversationId = message.conversationId
          this.showWelcome = false
        }
        return message
      }
      return null
    },

    // 查找AI消息对应的用户询问
    findUserMessageForAI(aiMessageId) {
      // 找到AI消息
      const aiMessage = this.messages.find(msg => msg.id === aiMessageId && msg.type === 'ai')
      if (!aiMessage) return null

      // 在同一对话中查找AI消息之前的最近一条用户消息（包括图片消息）
      const conversationMessages = this.messages
        .filter(msg => msg.conversationId === aiMessage.conversationId)
        .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))

      const aiMessageIndex = conversationMessages.findIndex(msg => msg.id === aiMessageId)
      if (aiMessageIndex === -1) return null

      // 从AI消息位置向前查找最近的用户消息或图片消息
      for (let i = aiMessageIndex - 1; i >= 0; i--) {
        const msg = conversationMessages[i]
        if (msg.type === 'user' || msg.type === 'image' || msg.type === 'multi-image') {
          return msg
        }
      }

      return null
    },

    // 将图片添加到照片墙
    addImagesToPhotoWall(images) {
      if (!images || images.length === 0) return

      // 为每张图片创建照片墙条目
      const newPhotoWallImages = images.map(image => ({
        id: `photowall_${Date.now()}_${Math.random()}`,
        url: image.url,
        name: image.name,
        timestamp: new Date(),
        conversationId: this.currentConversationId,
        messageId: image.id, // 原始消息ID
        isFromUserQuery: true // 标记这是来自用户询问的图片
      }))

      // 添加到照片墙，避免重复
      newPhotoWallImages.forEach(newImage => {
        const exists = this.photoWallImages.some(existing =>
          existing.url === newImage.url && existing.messageId === newImage.messageId
        )
        if (!exists) {
          this.photoWallImages.unshift(newImage) // 添加到开头，最新的在前面
        }
      })

      // 重新排序照片墙图片
      this.photoWallImages.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
    },

    // API相关方法
    setApiError(error) {
      this.apiError = error
    },

    clearApiError() {
      this.apiError = null
    },

    setConnectionStatus(status) {
      this.connectionStatus = status
    },

    // 获取对话上下文（用于API调用）
    getConversationContext(limit = 10) {
      return this.currentMessages
        .slice(-limit)
        .map(msg => ({
          role: msg.type === 'user' ? 'user' : 'assistant',
          content: msg.content,
          timestamp: msg.timestamp
        }))
    },

    // 模型选择相关方法
    setSelectedModel(modelId) {
      this.selectedModel = modelId
      // 保存到本地存储
      localStorage.setItem('dolphin-selected-model', modelId)
    },

    getSelectedModel() {
      return this.availableModels.find(model => model.id === this.selectedModel)
    },

    // 从API获取模型列表
    async fetchModels() {
      this.modelsLoading = true
      this.modelsError = null

      try {
        console.log('🤖 开始获取模型列表...')
        const result = await ModelService.getModelList()

        if (result.success && result.data) {
          console.log('🤖 API返回的原始数据:', result.data)

          // 格式化模型数据
          const formattedModels = ModelService.formatModels(result.data)
          console.log('🤖 格式化后的模型数据:', formattedModels)

          if (formattedModels.length > 0) {
            this.availableModels = formattedModels
            console.log('🤖 模型列表更新成功，当前可用模型:', this.availableModels)

            // 如果当前选中的模型不在新列表中，选择第一个模型
            const currentModel = this.getSelectedModel()
            if (!currentModel) {
              this.setSelectedModel(formattedModels[0].id)
              console.log('🤖 自动选择第一个模型:', formattedModels[0].name)
            }
          } else {
            console.warn('🤖 格式化后的模型列表为空，保持默认模型')
          }
        } else {
          throw new Error(result.message || '获取模型列表失败')
        }
      } catch (error) {
        console.error('🤖 获取模型列表失败:', error)
        this.modelsError = error.message
        // 保持默认模型列表，不清空
      } finally {
        this.modelsLoading = false
      }
    },

    // 初始化模型相关数据
    initModels() {
      // 从本地存储恢复选中的模型
      const savedModel = localStorage.getItem('dolphin-selected-model')
      if (savedModel) {
        this.selectedModel = savedModel
      }

      // 获取最新的模型列表
      this.fetchModels()
    },

    // 清除模型错误
    clearModelsError() {
      this.modelsError = null
    }

  }
})
