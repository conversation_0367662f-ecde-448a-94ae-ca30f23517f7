/**
 * plugins/vuetify.js
 *
 * Framework documentation: https://vuetifyjs.com`
 */

// Styles
import "@mdi/font/css/materialdesignicons.css"
import "vuetify/styles"

// Composables
import { createVuetify } from "vuetify"

// https://vuetifyjs.com/en/introduction/why-vuetify/#feature-guides
export default createVuetify({
  theme: {
    defaultTheme: "light",
    themes: {
      light: {
        dark: false,
        colors: {
          primary: "#1976d2",
          secondary: "#42a5f5",
          accent: "#82b1ff",
          error: "#ff5252",
          info: "#2196f3",
          success: "#4caf50",
          warning: "#ffc107",
          surface: "#ffffff",
          background: "#fafafa",
          "on-surface": "#000000",
          "on-background": "#000000",
          "surface-variant": "#f5f5f5",
          "on-surface-variant": "#424242",
        },
      },
      dark: {
        dark: true,
        colors: {
          primary: "#2196f3",
          secondary: "#64b5f6",
          accent: "#82b1ff",
          error: "#ff5252",
          info: "#2196f3",
          success: "#4caf50",
          warning: "#ffc107",
          surface: "#1e1e1e",
          background: "#121212",
          "on-surface": "#ffffff",
          "on-background": "#ffffff",
          "surface-variant": "#2d2d2d",
          "on-surface-variant": "#e0e0e0",
        },
      },
    },
  },
})
