<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  icon: {
    type: String,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  clickable: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['click'])

// 计算属性
const cardClasses = computed(() => ({
  'feature-card': true,
  'feature-card--clickable': props.clickable
}))

// 方法
const handleClick = () => {
  if (props.clickable) {
    emit('click', {
      title: props.title,
      icon: props.icon
    })
  }
}
</script>

<template>
  <div
    :class="cardClasses"
    @click="handleClick"
  >
    <div class="card-content">
      <!-- 图标背景圆圈 -->
      <div class="icon-wrapper">
        <div class="icon-container">
          <img
            :src="icon"
            :alt="title"
            class="feature-icon"
          />
        </div>
      </div>

      <!-- 标题 -->
      <h3 class="feature-title">
        {{ title }}
      </h3>
    </div>
  </div>
</template>

<style scoped>
.feature-card {
  height: 100%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 20px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
  overflow: hidden;
  position: relative;
  backdrop-filter: blur(10px);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 20px;
}

.feature-card--clickable {
  cursor: pointer;
}

.feature-card--clickable:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12), 0 8px 16px rgba(0, 0, 0, 0.08);
  border-color: rgba(0, 0, 0, 0.1);
}

.feature-card--clickable:hover::before {
  opacity: 1;
}

.feature-card--clickable:active {
  transform: translateY(-4px) scale(1.01);
  transition: all 0.1s ease;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 28px 20px;
  height: 100%;
  min-height: 140px;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.icon-wrapper {
  position: relative;
  margin-bottom: 16px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  border-radius: 18px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.feature-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.icon-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-radius: 18px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-card--clickable:hover .icon-wrapper {
  transform: scale(1.1) rotate(5deg);
}

.feature-card--clickable:hover .icon-container::before {
  opacity: 1;
}



.feature-title {
  font-size: 1rem;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1.3;
  margin: 0;
  letter-spacing: -0.02em;
  transition: all 0.3s ease;
}

.feature-card--clickable:hover .feature-title {
  color: #1a252f;
  transform: translateY(-2px);
}

/* 深色主题适配 */
[data-theme="dark"] .feature-card {
  background: linear-gradient(145deg, #2a2a2a 0%, #1e1e1e 100%);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .feature-card::before {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

[data-theme="dark"] .feature-title {
  color: #e0e0e0;
}

[data-theme="dark"] .feature-card--clickable:hover .feature-title {
  color: #ffffff;
}

[data-theme="dark"] .icon-container::before {
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-content {
    padding: 20px 16px;
    min-height: 120px;
  }

  .icon-container {
    width: 50px;
    height: 50px;
    border-radius: 15px;
  }

  .icon-wrapper {
    margin-bottom: 12px;
  }

  .feature-title {
    font-size: 0.9rem;
  }

  .feature-card--clickable:hover {
    transform: translateY(-4px) scale(1.01);
  }
}
</style>
