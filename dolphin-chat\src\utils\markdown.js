import MarkdownIt from "markdown-it"
import markdownItAttrs from "markdown-it-attrs"
import markdownItContainer from "markdown-it-container"
import markdownItFootnote from "markdown-it-footnote"
import hljs from "highlight.js"

// 创建 markdown-it 实例
const md = new MarkdownIt({
  html: true, // 允许HTML标签
  xhtmlOut: false, // 使用 '>' 来关闭单标签
  breaks: false, // 不自动转换段落里的 '\n' 到 <br>，让列表正确渲染
  langPrefix: "language-", // 给围栏代码块的CSS语言前缀
  linkify: true, // 将类似URL的文本自动转换为链接
  typographer: true, // 启用一些语言中性的替换 + 引号美化

  // 高亮函数，会返回转义的HTML
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return (
          '<pre class="hljs"><code>' +
          hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
          "</code></pre>"
        )
      } catch (__) {
        // Ignore highlighting errors
      }
    }

    return '<pre class="hljs"><code>' + md.utils.escapeHtml(str) + "</code></pre>"
  },
})

// 使用插件
md.use(markdownItAttrs)
md.use(markdownItFootnote)

// 配置容器插件
md.use(markdownItContainer, "warning", {
  validate: function (params) {
    return params.trim().match(/^warning\s+(.*)$/)
  },
  render: function (tokens, idx) {
    const m = tokens[idx].info.trim().match(/^warning\s+(.*)$/)
    if (tokens[idx].nesting === 1) {
      return (
        '<div class="warning custom-block"><p class="custom-block-title">' +
        md.utils.escapeHtml(m[1]) +
        "</p>\n"
      )
    } else {
      return "</div>\n"
    }
  },
})

md.use(markdownItContainer, "tip", {
  validate: function (params) {
    return params.trim().match(/^tip\s+(.*)$/)
  },
  render: function (tokens, idx) {
    const m = tokens[idx].info.trim().match(/^tip\s+(.*)$/)
    if (tokens[idx].nesting === 1) {
      return (
        '<div class="tip custom-block"><p class="custom-block-title">' +
        md.utils.escapeHtml(m[1]) +
        "</p>\n"
      )
    } else {
      return "</div>\n"
    }
  },
})

// 文本预处理函数
function preprocess(text) {
  if (typeof text !== "string") return ""

  // 处理特殊的markdown语法
  let processed = text

  // 确保标题前后有换行，但不影响列表
  processed = processed.replace(/^(#{1,6}\s+.+)$/gm, (match, p1, offset, string) => {
    // 检查前一个字符是否是换行符，如果不是则添加
    const prevChar = string[offset - 1]
    const prefix = prevChar && prevChar !== "\n" ? "\n" : ""
    return prefix + p1 + "\n"
  })

  // 处理代码块
  processed = processed.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
    return "\n```" + (lang || "") + "\n" + code + "```\n"
  })

  // 确保有序列表项之间有正确的换行
  // 匹配格式：数字. **标题**：内容
  processed = processed.replace(/(\d+\.\s+\*\*[^*]+\*\*：[^\n]+)/g, (match, p1, offset, string) => {
    // 检查前一个字符，确保列表项前有换行
    const prevChar = string[offset - 1]
    const prefix = prevChar && prevChar !== "\n" && offset > 0 ? "\n" : ""

    // 检查后一个字符，确保列表项后有换行（如果后面还有内容）
    const nextIndex = offset + match.length
    const nextChar = string[nextIndex]
    const suffix = nextChar && nextChar !== "\n" && nextIndex < string.length ? "\n" : ""

    return prefix + p1 + suffix
  })

  // 处理普通有序列表项
  processed = processed.replace(/(\d+\.\s+[^\n]+)/g, (match, p1, offset, string) => {
    // 避免重复处理已经处理过的带粗体的列表项
    if (p1.includes("**")) return match

    const prevChar = string[offset - 1]
    const prefix = prevChar && prevChar !== "\n" && offset > 0 ? "\n" : ""

    const nextIndex = offset + match.length
    const nextChar = string[nextIndex]
    const suffix = nextChar && nextChar !== "\n" && nextIndex < string.length ? "\n" : ""

    return prefix + p1 + suffix
  })

  return processed
}

// Markdown渲染函数
export function renderMarkdown(text) {
  try {
    if (!text || typeof text !== "string") return ""

    const preprocessed = preprocess(text)
    const rendered = md.render(preprocessed)

    return rendered
  } catch (err) {
    console.error("Markdown渲染失败", err)
    // 降级处理：简单的换行转换
    return text.replace(/\n/g, "<br>")
  }
}

// 行内Markdown渲染函数
export function renderMarkdownInline(text) {
  try {
    if (!text || typeof text !== "string") return ""

    const rendered = md.renderInline(text)
    return rendered
  } catch (err) {
    console.error("Markdown行内渲染失败", err)
    return text
  }
}

// 导出
export const parseMarkdown = renderMarkdown
export default md
