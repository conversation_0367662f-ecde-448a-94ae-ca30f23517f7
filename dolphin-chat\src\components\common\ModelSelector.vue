<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useChatStore } from '@/stores/baseStore'

// 使用聊天store
const chatStore = useChatStore()

// 响应式数据
const showDropdown = ref(false)
const selectorRef = ref(null)

// 计算属性
const selectedModel = computed(() => chatStore.getSelectedModel())
const availableModels = computed(() => chatStore.availableModels)
const modelsLoading = computed(() => chatStore.modelsLoading)
const modelsError = computed(() => chatStore.modelsError)

// 方法
const selectModel = (modelId) => {
  chatStore.setSelectedModel(modelId)
  showDropdown.value = false
}

const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

// 点击外部关闭下拉菜单
const handleGlobalClick = (event) => {
  if (selectorRef.value && !selectorRef.value.contains(event.target)) {
    showDropdown.value = false
  }
}

// 重新获取模型列表
const refreshModels = () => {
  chatStore.fetchModels()
}

// 清除错误
const clearError = () => {
  chatStore.clearModelsError()
}

// 生命周期钩子
onMounted(() => {
  document.addEventListener('click', handleGlobalClick)
  // 初始化模型数据
  chatStore.initModels()
})

onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick)
})
</script>

<template>
  <div class="model-selector" ref="selectorRef">
    <!-- 模型选择按钮 -->
    <VBtn
      variant="outlined"
      class="model-selector-btn"
      :class="{ 'active': showDropdown }"
      @click="toggleDropdown"
    >
      <div class="model-info">
        <VIcon
          :icon="selectedModel?.icon"
          :color="selectedModel?.color"
          size="20"
          class="model-icon"
        />
        <div class="model-text">
          <span class="model-name">{{ selectedModel?.name }}</span>
          <span class="model-desc">{{ selectedModel?.description }}</span>
        </div>
      </div>
      <VIcon
        :icon="showDropdown ? 'mdi-chevron-up' : 'mdi-chevron-down'"
        size="16"
        class="dropdown-icon"
      />
    </VBtn>

    <!-- 下拉菜单 -->
    <Transition name="dropdown">
      <div v-if="showDropdown" class="model-dropdown">
        <div class="dropdown-header">
          <span class="dropdown-title">选择模型</span>
          <VBtn
            v-if="modelsError"
            icon="mdi-refresh"
            size="small"
            variant="text"
            color="primary"
            @click="refreshModels"
            :loading="modelsLoading"
            class="refresh-btn"
          />
        </div>
        <!-- 加载状态 -->
        <div v-if="modelsLoading" class="loading-container">
          <VProgressCircular
            indeterminate
            color="primary"
            size="24"
          />
          <span class="loading-text">加载模型列表中...</span>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="modelsError" class="error-container">
          <VIcon icon="mdi-alert-circle" color="error" size="20" />
          <span class="error-text">{{ modelsError }}</span>
          <VBtn
            size="small"
            variant="text"
            color="primary"
            @click="clearError"
          >
            关闭
          </VBtn>
        </div>

        <!-- 模型列表 -->
        <div v-else class="model-list">
          <div
            v-for="model in availableModels"
            :key="model.id"
            class="model-item"
            :class="{ 'selected': model.id === selectedModel?.id }"
            @click="selectModel(model.id)"
          >
            <VIcon
              :icon="model.icon"
              :color="model.color"
              size="20"
              class="model-item-icon"
            />
            <div class="model-item-content">
              <div class="model-item-name">{{ model.name }}</div>
              <div class="model-item-desc">{{ model.description }}</div>
            </div>
            <VIcon
              v-if="model.id === selectedModel?.id"
              icon="mdi-check"
              color="primary"
              size="16"
              class="check-icon"
            />
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<style scoped>
.model-selector {
  position: relative;
  display: inline-block;
}

.model-selector-btn {
  min-width: 160px;
  height: 36px;
  padding: 4px 12px;
  border-radius: 8px;
  border: 1px solid rgba(102, 126, 234, 0.08);
  background: var(--app-bg-primary);
  transition: all 0.2s ease;
  text-transform: none;
  font-weight: normal;
}

.model-selector-btn:hover {
  border-color: rgba(102, 126, 234, 0.15);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.05);
}

.model-selector-btn.active {
  border-color: rgba(102, 126, 234, 0.25);
  box-shadow: 0 2px 12px rgba(102, 126, 234, 0.08);
}

.model-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.model-icon {
  flex-shrink: 0;
}

.model-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  flex: 1;
}

.model-name {
  font-size: 13px;
  font-weight: 500;
  color: var(--app-text-primary);
  line-height: 1.2;
  transition: color 0.3s ease;
}

.model-desc {
  font-size: 10px;
  color: var(--app-text-secondary);
  line-height: 1.2;
  margin-top: 1px;
  transition: color 0.3s ease;
}

.dropdown-icon {
  flex-shrink: 0;
  margin-left: 4px;
  transition: transform 0.2s ease;
}

.model-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 4px;
  background: var(--app-bg-primary);
  border: 1px solid var(--app-border-color);
  border-radius: 12px;
  box-shadow: 0 8px 24px var(--app-shadow);
  z-index: 1000;
  overflow: hidden;
  transition: all 0.3s ease;
}

.dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px 8px 16px;
  border-bottom: 1px solid var(--app-border-light);
  transition: border-color 0.3s ease;
}

.dropdown-title {
  font-size: 12px;
  font-weight: 500;
  color: var(--app-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: color 0.3s ease;
}

.refresh-btn {
  min-width: auto !important;
  width: 24px;
  height: 24px;
}

.model-list {
  max-height: 280px;
  overflow-y: auto;
}

.model-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.model-item:hover {
  background-color: var(--app-hover-bg);
}

.model-item.selected {
  background-color: var(--app-active-bg);
}

.model-item-icon {
  flex-shrink: 0;
}

.model-item-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  text-align: left;
}

.model-item-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--app-text-primary);
  line-height: 1.2;
  transition: color 0.3s ease;
}

.model-item-desc {
  font-size: 12px;
  color: var(--app-text-secondary);
  line-height: 1.2;
  margin-top: 2px;
  transition: color 0.3s ease;
}

.check-icon {
  flex-shrink: 0;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 16px;
  justify-content: center;
}

.loading-text {
  font-size: 14px;
  color: var(--app-text-secondary);
  transition: color 0.3s ease;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: rgba(244, 67, 54, 0.1);
  border-left: 3px solid #f44336;
}

.error-text {
  font-size: 12px;
  color: #f44336;
  flex: 1;
}

/* 下拉动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease;
  transform-origin: top;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: scaleY(0.8) translateY(-8px);
}

.dropdown-enter-to,
.dropdown-leave-from {
  opacity: 1;
  transform: scaleY(1) translateY(0);
}

/* 滚动条样式 */
.model-list::-webkit-scrollbar {
  width: 4px;
}

.model-list::-webkit-scrollbar-track {
  background: transparent;
}

.model-list::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

.model-list::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 深色主题适配 */
[data-theme="dark"] .model-selector-btn {
  background: var(--app-bg-secondary);
  border-color: rgba(255, 255, 255, 0.08);
  color: var(--app-text-primary);
}

[data-theme="dark"] .model-selector-btn:hover {
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .model-selector-btn.active {
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 2px 12px rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .model-dropdown {
  background: var(--app-bg-secondary);
  border-color: rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .dropdown-icon {
  color: var(--app-text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .model-selector-btn {
    min-width: 140px;
    height: 32px;
    padding: 3px 8px;
  }

  .model-name {
    font-size: 12px;
  }

  .model-desc {
    font-size: 9px;
  }

  .model-info {
    gap: 6px;
  }
}
</style>
