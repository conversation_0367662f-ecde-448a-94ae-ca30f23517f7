# 开发环境配置

# 应用标题
VITE_APP_TITLE=Dolphin AI - 开发环境

# API基础地址 - 使用相对路径启用Vite代理
VITE_API_BASE_URL=/api

# WebSocket地址 - 保持完整URL，因为WebSocket不走HTTP代理
VITE_WS_URL=ws://192.168.1.111:8080/ws

# 是否启用Mock数据
VITE_USE_MOCK=false

# 是否启用调试模式
VITE_DEBUG=true

# 日志级别 (error | warn | info | debug)
VITE_LOG_LEVEL=debug

# 是否启用性能监控
VITE_ENABLE_PERFORMANCE=true

# 文件上传地址 - 使用相对路径启用代理
VITE_UPLOAD_URL=/api/v1/file/upload

# CDN地址 - 静态资源可能需要完整URL
VITE_CDN_URL=http://192.168.1.111:8080/static

# 第三方服务配置
VITE_GOOGLE_ANALYTICS_ID=

# 错误监控服务
VITE_SENTRY_DSN=

# 地图服务API Key
VITE_MAP_API_KEY=
