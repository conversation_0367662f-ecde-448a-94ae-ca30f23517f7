import { defineStore } from "pinia"
import { ref } from "vue"
import { AuthService } from "@/api/auth"

export const useAuthStore = defineStore("auth", () => {
  // 状态
  const isLoggedIn = ref(false)
  const user = ref(null)
  const loading = ref(false)
  const error = ref("")

  // 登录方法
  const login = async (credentials) => {
    loading.value = true
    error.value = ""

    try {
      // 调用真实 API
      const result = await AuthService.login(credentials)

      if (result.success) {
        // 设置用户信息
        user.value = result.data.user
        isLoggedIn.value = true

        // 保存到 localStorage (AuthService 已经处理了 token 保存)
        localStorage.setItem("user", JSON.stringify(user.value))
        localStorage.setItem("isLoggedIn", "true")

        return { success: true }
      } else {
        throw new Error(result.message || "登录失败")
      }
    } catch (err) {
      error.value = err.message || "登录失败，请检查网络连接"
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  // 登出方法
  const logout = () => {
    // 直接清除本地状态，不调用API
    user.value = null
    isLoggedIn.value = false
    error.value = ""

    // 清除 localStorage
    localStorage.removeItem("user")
    localStorage.removeItem("isLoggedIn")
    localStorage.removeItem("token")
  }

  // 初始化认证状态
  const initAuth = () => {
    const savedUser = localStorage.getItem("user")
    const savedLoginStatus = localStorage.getItem("isLoggedIn")
    const token = localStorage.getItem("token")

    // 检查是否有完整的认证信息
    if (savedUser && savedLoginStatus === "true" && token) {
      try {
        user.value = JSON.parse(savedUser)
        isLoggedIn.value = true
      
      } catch (err) {
        console.error('用户信息解析失败:', err)
        // 清除无效数据
        logout()
      }
    } else {
    
      // 认证信息不完整，清除所有状态
      logout()
    }
  }

  // 清除错误
  const clearError = () => {
    error.value = ""
  }

  // 更新用户信息
  const updateUser = (userData) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      // 更新 localStorage
      localStorage.setItem("user", JSON.stringify(user.value))
    }
  }

  return {
    // 状态
    isLoggedIn,
    user,
    loading,
    error,

    // 方法
    login,
    logout,
    initAuth,
    clearError,
    updateUser,
  }
})
