/**
 * 主题管理 Composable
 * 统一管理应用的主题切换逻辑
 */

import { computed, watch, nextTick } from 'vue'
import { useAppStore } from '@/stores/baseStore'
import { useTheme as useVuetifyTheme } from 'vuetify'

export function useTheme() {
  const appStore = useAppStore()
  const vuetifyTheme = useVuetifyTheme()

  // 计算属性
  const currentTheme = computed(() => appStore.currentTheme)
  const themeMode = computed(() => appStore.theme)
  const themeDisplayName = computed(() => appStore.themeDisplayName)

  // 主题选项
  const themeOptions = [
    { value: 'system', label: '跟随系统', icon: 'mdi-theme-light-dark' },
    { value: 'light', label: '浅色主题', icon: 'mdi-white-balance-sunny' },
    { value: 'dark', label: '深色主题', icon: 'mdi-moon-waning-crescent' }
  ]

  // 设置主题
  const setTheme = (theme) => {
    appStore.setTheme(theme)
  }

  // 切换主题
  const toggleTheme = () => {
    appStore.toggleTheme()
  }

  // 应用主题到 DOM 和 Vuetify
  const applyTheme = (theme) => {
    // 设置 DOM 的 data-theme 属性
    if (typeof document !== 'undefined') {
      document.documentElement.setAttribute('data-theme', theme)
      // 强制设置 body 的背景色
      document.body.style.backgroundColor = theme === 'dark' ? '#121212' : '#fafafa'
    }

    // 同步 Vuetify 主题
    if (vuetifyTheme) {
      vuetifyTheme.global.name.value = theme
    }
  }

  // 初始化主题
  const initTheme = () => {
    // 初始化 store 中的主题系统
    appStore.initTheme()

    // 立即应用当前主题
    applyTheme(appStore.actualTheme)
  }

  // 监听主题变化并应用
  watch(
    () => appStore.actualTheme,
    (newTheme) => {
      nextTick(() => {
        applyTheme(newTheme)
      })
    },
    { immediate: true }
  )

  // 获取主题图标
  const getThemeIcon = (theme) => {
    const iconMap = {
      'system': 'mdi-theme-light-dark',
      'light': 'mdi-white-balance-sunny',
      'dark': 'mdi-moon-waning-crescent'
    }
    return iconMap[theme] || 'mdi-theme-light-dark'
  }

  // 获取主题颜色
  const getThemeColor = (theme) => {
    const colorMap = {
      'system': 'primary',
      'light': 'orange',
      'dark': 'blue-grey'
    }
    return colorMap[theme] || 'primary'
  }

  // 检查是否为深色主题
  const isDark = computed(() => currentTheme.value === 'dark')

  // 检查是否为浅色主题
  const isLight = computed(() => currentTheme.value === 'light')

  // 检查是否跟随系统
  const isSystem = computed(() => themeMode.value === 'system')

  return {
    // 状态
    currentTheme,
    themeMode,
    themeDisplayName,
    isDark,
    isLight,
    isSystem,

    // 选项
    themeOptions,

    // 方法
    setTheme,
    toggleTheme,
    initTheme,
    getThemeIcon,
    getThemeColor,

    // 工具方法
    applyTheme
  }
}
