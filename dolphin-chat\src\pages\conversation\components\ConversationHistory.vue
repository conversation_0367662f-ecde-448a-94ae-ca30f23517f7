<script setup>
import { ref, computed, nextTick, onMounted, onUnmounted } from "vue"

// 定义emits
const emit = defineEmits(["conversation-select", "conversation-rename", "conversation-delete"])

// 响应式数据 - 医疗聊天知识相关对话
const conversations = ref([
  {
    id: 1,
    title: "心脏超声检查报告解读分析",
    date: new Date(),
    category: "7天内",
  },
  {
    id: 2,
    title: "腹部超声异常回声诊断要点",
    date: new Date(Date.now() - 24 * 60 * 60 * 1000),
    category: "30天内",
  },
  {
    id: 3,
    title: "甲状腺结节超声分级标准讨论",
    date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    category: "30天内",
  },
  {
    id: 4,
    title: "胎儿超声筛查技术规范",
    date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    category: "30天内",
  },
  {
    id: 5,
    title: "血管超声多普勒检查方法",
    date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    category: "30天内",
  },
  {
    id: 6,
    title: "乳腺超声BI-RADS分类系统",
    date: new Date("2025-04-15"),
    category: "2025-04",
  },
  {
    id: 7,
    title: "肾脏超声检查操作技巧",
    date: new Date("2025-04-10"),
    category: "2025-04",
  },
  {
    id: 8,
    title: "超声造影剂临床应用指南",
    date: new Date("2025-04-08"),
    category: "2025-04",
  },
  {
    id: 9,
    title: "肌骨超声诊断技术要点",
    date: new Date("2025-04-05"),
    category: "2025-04",
  },
  {
    id: 10,
    title: "胸腔积液超声诊断技术",
    date: new Date("2025-04-01"),
    category: "2025-04",
  },
  {
    id: 11,
    title: "妇科超声检查标准流程",
    date: new Date("2025-03-28"),
    category: "2025-04",
  },
  {
    id: 12,
    title: "颈动脉超声斑块评估方法",
    date: new Date("2025-03-25"),
    category: "2025-04",
  },
  {
    id: 13,
    title: "肝脏超声弥漫性病变诊断",
    date: new Date("2025-03-20"),
    category: "2025-04",
  },
  {
    id: 14,
    title: "胆囊超声检查注意事项",
    date: new Date("2025-03-15"),
    category: "2025-04",
  },
  {
    id: 15,
    title: "脾脏超声测量标准参考值",
    date: new Date("2025-03-10"),
    category: "2025-04",
  },
])

const activeConversation = ref(null)
const showMenuForId = ref(null) // 当前显示菜单的对话ID
const renamingId = ref(null) // 当前正在重命名的对话ID
const renameValue = ref('') // 重命名输入框的值

// 计算属性 - 按日期分组对话，DeepSeek样式
const groupedConversations = computed(() => {
  const groups = {}
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
  const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)

  conversations.value.forEach((conv) => {
    const convDate = new Date(conv.date)
    const convDateOnly = new Date(convDate.getFullYear(), convDate.getMonth(), convDate.getDate())

    let category
    if (convDateOnly >= sevenDaysAgo) {
      category = "7天内"
    } else if (convDateOnly >= thirtyDaysAgo) {
      category = "30天内"
    } else {
      // 使用年月格式
      category = `${convDate.getFullYear()}-${String(convDate.getMonth() + 1).padStart(2, "0")}`
    }

    if (!groups[category]) {
      groups[category] = []
    }
    groups[category].push(conv)
  })

  // 按时间顺序排序分组
  const sortedGroups = {}
  const order = ["7天内", "30天内"]

  // 先添加固定顺序的分组
  order.forEach((key) => {
    if (groups[key]) {
      sortedGroups[key] = groups[key]
    }
  })

  // 再添加年月分组，按时间倒序
  Object.keys(groups)
    .filter((key) => !order.includes(key))
    .sort((a, b) => b.localeCompare(a))
    .forEach((key) => {
      sortedGroups[key] = groups[key]
    })

  return sortedGroups
})

// 计算属性 - 扁平化的对话列表，用于只显示一次时间标签
const flatConversations = computed(() => {
  const result = []
  const groups = groupedConversations.value
  const shownCategories = new Set()

  Object.keys(groups).forEach((category) => {
    const convs = groups[category]
    convs.forEach((conv, index) => {
      // 只在该分类第一次出现时显示时间标签
      const showCategoryHeader = !shownCategories.has(category) && index === 0

      if (showCategoryHeader) {
        shownCategories.add(category)
      }

      result.push({
        ...conv,
        category,
        showCategoryHeader,
      })
    })
  })

  return result
})

// 方法
const selectConversation = (conversation) => {
  activeConversation.value = conversation.id
  emit("conversation-select", conversation)
  console.log("选择对话:", conversation.title)
}

// 显示/隐藏菜单
const toggleMenu = (conversationId, event) => {
  event.stopPropagation() // 阻止事件冒泡，避免触发对话选择
  showMenuForId.value = showMenuForId.value === conversationId ? null : conversationId
}

// 关闭菜单
const closeMenu = () => {
  showMenuForId.value = null
}

// 开始重命名
const startRename = (conversation, event) => {
  event.stopPropagation()
  renamingId.value = conversation.id
  renameValue.value = conversation.title
  showMenuForId.value = null

  // 下一个tick后聚焦输入框
  nextTick(() => {
    const input = document.querySelector(`#rename-input-${conversation.id}`)
    if (input) {
      input.focus()
      input.select()
    }
  })
}

// 确认重命名
const confirmRename = (conversationId) => {
  if (renameValue.value.trim()) {
    const conversation = conversations.value.find(c => c.id === conversationId)
    if (conversation) {
      conversation.title = renameValue.value.trim()
      emit("conversation-rename", { id: conversationId, newTitle: renameValue.value.trim() })
    }
  }
  cancelRename()
}

// 取消重命名
const cancelRename = () => {
  renamingId.value = null
  renameValue.value = ''
}

// 删除对话
const deleteConversation = (conversationId, event) => {
  event.stopPropagation()
  if (confirm('确定要删除这个对话吗？')) {
    const index = conversations.value.findIndex(c => c.id === conversationId)
    if (index > -1) {
      conversations.value.splice(index, 1)
      emit("conversation-delete", conversationId)

      // 如果删除的是当前活跃对话，清除活跃状态
      if (activeConversation.value === conversationId) {
        activeConversation.value = null
      }
    }
  }
  showMenuForId.value = null
}

// 处理重命名输入框的键盘事件
const handleRenameKeydown = (event, conversationId) => {
  if (event.key === 'Enter') {
    confirmRename(conversationId)
  } else if (event.key === 'Escape') {
    cancelRename()
  }
}

// 全局点击事件处理器
const handleGlobalClick = (event) => {
  // 如果点击的不是菜单相关元素，关闭菜单
  if (!event.target.closest('.conversation-actions')) {
    closeMenu()
  }
}

// 生命周期钩子
onMounted(() => {
  document.addEventListener('click', handleGlobalClick)
})

onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick)
})
</script>

<template>
  <!-- 对话历史 - DeepSeek样式 -->
  <div class="conversation-history">
    <div class="conversation-list">
      <template v-for="conversation in flatConversations" :key="conversation.id">
        <!-- 时间分组标题 -->
        <div v-if="conversation.showCategoryHeader" class="time-category">
          {{ conversation.category }}
        </div>

        <!-- 对话项目 -->
        <div
          class="conversation-item"
          :class="{ active: activeConversation === conversation.id }"
          @click="selectConversation(conversation)">

          <!-- 重命名模式 -->
          <div v-if="renamingId === conversation.id" class="rename-container">
            <input
              :id="`rename-input-${conversation.id}`"
              v-model="renameValue"
              class="rename-input"
              @keydown="handleRenameKeydown($event, conversation.id)"
              @blur="confirmRename(conversation.id)"
              @click.stop
            />
          </div>

          <!-- 正常显示模式 - 只显示标题 -->
          <div v-else class="conversation-content">
            <div class="conversation-title">{{ conversation.title }}</div>

            <!-- 三点菜单按钮 -->
            <div class="conversation-actions">
              <button
                class="menu-button"
                @click="toggleMenu(conversation.id, $event)"
                :class="{ active: showMenuForId === conversation.id }"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <circle cx="12" cy="5" r="2"/>
                  <circle cx="12" cy="12" r="2"/>
                  <circle cx="12" cy="19" r="2"/>
                </svg>
              </button>

              <!-- 下拉菜单 -->
              <div
                v-if="showMenuForId === conversation.id"
                class="dropdown-menu"
                @click.stop
              >
                <div class="menu-item" @click="startRename(conversation, $event)">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                  </svg>
                  重命名
                </div>
                <div class="menu-item delete" @click="deleteConversation(conversation.id, $event)">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                  </svg>
                  删除
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped>
.conversation-history {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  padding: 0;
  background-color: var(--app-bg-primary);
  transition: background-color 0.3s ease;
}

/* 滚动条样式 - 简洁设计 */
.conversation-history::-webkit-scrollbar {
  width: 4px;
}

.conversation-history::-webkit-scrollbar-track {
  background: transparent;
}

.conversation-history::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  transition: background 0.3s ease;
}

.conversation-history::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

.conversation-list {
  padding: 12px 16px;
  background-color: var(--app-bg-primary);
  transition: background-color 0.3s ease;
}

/* 时间分组标题 - DeepSeek样式 */
.time-category {
  font-size: 12px;
  font-weight: 500;
  color: var(--app-text-secondary);
  margin: 16px 0 8px 0;
  padding: 0 4px;
  opacity: 0.8;
}

.time-category:first-child {
  margin-top: 0;
}

.conversation-item {
  padding: 8px 12px;
  margin-bottom: 2px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.15s ease;
  background-color: transparent;
  position: relative;
  border: none;
}

.conversation-item:hover {
  background-color: var(--app-hover-bg);
}

.conversation-item.active {
  background-color: var(--app-active-bg);
}

.conversation-title {
  font-size: 13px;
  font-weight: 400;
  color: var(--app-text-primary);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: color 0.3s ease;
  flex: 1;
  min-width: 0;
}

.conversation-item.active .conversation-title {
  color: var(--v-theme-primary);
  font-weight: 500;
}

/* 对话内容布局 - 简化版 */
.conversation-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
}

/* 重命名容器 */
.rename-container {
  width: 100%;
}

.rename-input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid var(--v-theme-primary);
  border-radius: 4px;
  font-size: 13px;
  font-weight: 400;
  color: var(--app-text-primary);
  background-color: var(--app-bg-primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
  transition: all 0.3s ease;
}

/* 操作按钮区域 */
.conversation-actions {
  position: relative;
  flex-shrink: 0;
}

.menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: transparent;
  border-radius: 3px;
  cursor: pointer;
  color: var(--app-text-secondary);
  opacity: 0;
  transition: all 0.15s ease;
}

.conversation-item:hover .menu-button {
  opacity: 1;
}

.menu-button:hover,
.menu-button.active {
  background-color: var(--app-hover-bg);
  color: var(--app-text-primary);
}

.menu-button.active {
  opacity: 1;
}

/* 下拉菜单 */
.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 100px;
  background: var(--app-bg-primary);
  border: 1px solid var(--app-border-color);
  border-radius: 6px;
  box-shadow: 0 4px 12px var(--app-shadow);
  z-index: 1000;
  overflow: hidden;
  margin-top: 2px;
  transition: all 0.3s ease;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  font-size: 12px;
  color: var(--app-text-primary);
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.3s ease;
  gap: 6px;
}

.menu-item:hover {
  background-color: var(--app-hover-bg);
}

.menu-item.delete {
  color: #d32f2f;
}

.menu-item.delete:hover {
  background-color: #ffebee;
}

.menu-item svg {
  flex-shrink: 0;
}
</style>
