<script setup>
import logoUrl from "@/assets/logo.png"
import aiImageUrl from "@/assets/images/ai.png"

// Props
const props = defineProps({
  isCollapsed: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(["logo-click"])

// 方法
const handleLogoClick = () => {
  emit("logo-click")
}
</script>

<template>
  <!-- 展开状态的品牌标识 -->
  <div v-if="!isCollapsed" class="brand-content">
    <div class="brand-info">
      <img :src="aiImageUrl" alt="Dolphin AI" class="brand-image" @click="handleLogoClick" />
    </div>
  </div>

  <!-- 收缩状态的品牌标识 -->
  <div v-if="isCollapsed" class="collapsed-logo-container" @click="handleLogoClick">
    <img :src="logoUrl" alt="Dolphin AI Logo" class="collapsed-logo" />
  </div>
</template>

<style scoped>
.brand-content {
  display: flex;
  align-items: center;
  flex: 1;
}



.brand-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex: 1;
  padding-left: 4px;
}

.brand-image {
  height: 51px;
  width: auto;
  max-width: 180px;
  object-fit: contain;
  transition: all 0.3s ease;
  cursor: pointer;
}

.brand-image:hover {
  transform: scale(1.02);
}

.collapsed-logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.collapsed-logo-container:hover {
  transform: translateY(-2px) scale(1.08);
  background-color: rgba(25, 118, 210, 0.08);
  box-shadow: 0 6px 20px rgba(25, 118, 210, 0.15);
}

.collapsed-logo-container:active {
  transform: translateY(0) scale(1.04);
  transition: all 0.1s ease;
}

.collapsed-logo {
  width: 28px;
  height: 28px;
  object-fit: contain;
  transition: all 0.3s ease;
}

/* 暗黑模式下增加logo亮度 */
[data-theme="dark"] .collapsed-logo {
  filter: brightness(1.3) contrast(1.1) saturate(1.2);
}

[data-theme="dark"] .brand-image {
  filter: brightness(1.3) contrast(1.1) saturate(1.2);
}
</style>
