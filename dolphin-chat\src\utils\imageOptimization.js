/**
 * 图片优化工具函数
 */

/**
 * 检测浏览器是否支持 WebP 格式
 * @returns {Promise<boolean>}
 */
export function supportsWebP() {
  return new Promise((resolve) => {
    const webP = new Image()
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2)
    }
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA'
  })
}

/**
 * 获取优化后的图片 URL
 * @param {string} originalSrc - 原始图片路径
 * @param {Object} options - 优化选项
 * @returns {Promise<string>}
 */
export async function getOptimizedImageSrc(originalSrc, options = {}) {
  const {
    format = 'auto', // 'webp', 'auto', 'original'
    quality = 80,
    width,
    height
  } = options

  // 如果是外部链接，直接返回
  if (originalSrc.startsWith('http')) {
    return originalSrc
  }

  // 检查是否支持 WebP
  const webpSupported = await supportsWebP()
  
  // 如果支持 WebP 且格式设置为 auto，则尝试使用 WebP
  if (webpSupported && format === 'auto') {
    const webpSrc = originalSrc.replace(/\.(png|jpg|jpeg)$/i, '.webp')
    
    // 检查 WebP 文件是否存在
    try {
      const response = await fetch(webpSrc, { method: 'HEAD' })
      if (response.ok) {
        return webpSrc
      }
    } catch (error) {
      console.warn('WebP 文件不存在，使用原始格式:', error)
    }
  }

  return originalSrc
}

/**
 * 预加载图片
 * @param {string|Array<string>} src - 图片路径或路径数组
 * @param {Object} options - 预加载选项
 * @returns {Promise<void>}
 */
export function preloadImages(src, options = {}) {
  const sources = Array.isArray(src) ? src : [src]
  const { timeout = 10000 } = options

  return Promise.allSettled(
    sources.map(imageSrc => 
      new Promise((resolve, reject) => {
        const img = new Image()
        
        const timer = setTimeout(() => {
          reject(new Error(`图片预加载超时: ${imageSrc}`))
        }, timeout)

        img.onload = () => {
          clearTimeout(timer)
          resolve(imageSrc)
        }

        img.onerror = () => {
          clearTimeout(timer)
          reject(new Error(`图片预加载失败: ${imageSrc}`))
        }

        img.src = imageSrc
      })
    )
  )
}

/**
 * 创建响应式图片 srcset
 * @param {string} baseSrc - 基础图片路径
 * @param {Array<number>} sizes - 尺寸数组
 * @returns {string}
 */
export function createResponsiveSrcset(baseSrc, sizes = [480, 768, 1024, 1440]) {
  const extension = baseSrc.split('.').pop()
  const baseName = baseSrc.replace(`.${extension}`, '')
  
  return sizes
    .map(size => `${baseName}_${size}w.${extension} ${size}w`)
    .join(', ')
}

/**
 * 图片懒加载观察器
 */
export class ImageLazyLoader {
  constructor(options = {}) {
    this.options = {
      rootMargin: '50px',
      threshold: 0.1,
      ...options
    }
    
    this.observer = new IntersectionObserver(
      this.handleIntersection.bind(this),
      this.options
    )
  }

  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target
        const src = img.dataset.src
        
        if (src) {
          img.src = src
          img.removeAttribute('data-src')
          this.observer.unobserve(img)
        }
      }
    })
  }

  observe(element) {
    this.observer.observe(element)
  }

  unobserve(element) {
    this.observer.unobserve(element)
  }

  disconnect() {
    this.observer.disconnect()
  }
}

/**
 * 图片压缩工具
 * @param {File} file - 图片文件
 * @param {Object} options - 压缩选项
 * @returns {Promise<Blob>}
 */
export function compressImage(file, options = {}) {
  const {
    quality = 0.8,
    maxWidth = 1920,
    maxHeight = 1080,
    format = 'image/jpeg'
  } = options

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      // 计算新的尺寸
      let { width, height } = img
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width
        width = maxWidth
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height
        height = maxHeight
      }

      canvas.width = width
      canvas.height = height

      // 绘制并压缩
      ctx.drawImage(img, 0, 0, width, height)
      
      canvas.toBlob(resolve, format, quality)
    }

    img.onerror = reject
    img.src = URL.createObjectURL(file)
  })
}
