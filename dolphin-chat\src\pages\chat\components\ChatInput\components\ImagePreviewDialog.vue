<template>
  <!-- 图片预览对话框 -->
  <VDialog 
    :model-value="show" 
    max-width="800" 
    @click:outside="$emit('close')"
    @update:model-value="handleDialogUpdate">
    <VCard v-if="selectedImage">
      <VCardTitle class="d-flex justify-space-between align-center">
        <span>{{ selectedImage.name || '图片预览' }}</span>
        <VBtn 
          icon="mdi-close" 
          size="small" 
          variant="text" 
          @click="$emit('close')" />
      </VCardTitle>
      <VCardText class="pa-0">
        <img 
          :src="selectedImage.url" 
          :alt="selectedImage.name" 
          class="preview-image" />
      </VCardText>
      <VCardActions>
        <VBtn
          prepend-icon="mdi-download"
          variant="outlined"
          @click="$emit('download', selectedImage)">
          下载
        </VBtn>
        <VSpacer />
        <VBtn 
          variant="text" 
          @click="$emit('close')"> 
          关闭 
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>

<script setup>
// 定义 props
defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  selectedImage: {
    type: Object,
    default: null,
  },
})

// 定义 emits
const emit = defineEmits(['close', 'download'])

// 处理对话框更新
const handleDialogUpdate = (value) => {
  if (!value) {
    emit('close')
  }
}
</script>

<style scoped>
/* 图片预览对话框样式 */
.preview-image {
  width: 100%;
  height: auto;
  max-height: 70vh;
  object-fit: contain;
  display: block;
}

/* 预览对话框的卡片样式 */
:deep(.v-dialog .v-card) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.v-dialog .v-card-title) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  padding: 16px 20px;
  font-weight: 600;
}

:deep(.v-dialog .v-card-actions) {
  padding: 16px 20px;
  background-color: #f8f9fa;
  border-top: 1px solid #e0e0e0;
}
</style>
