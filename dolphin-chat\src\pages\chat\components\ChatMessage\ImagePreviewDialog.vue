<template>
  <!-- 图片预览对话框 -->
  <VDialog v-model="showDialog" max-width="800" @click:outside="handleClose">
    <VCard v-if="imageUrl">
      <VCardTitle class="d-flex justify-space-between align-center">
        <span>{{ imageName || "图片预览" }}</span>
        <VBtn icon="mdi-close" size="small" variant="text" @click="handleClose" />
      </VCardTitle>
      <VCardText class="pa-0">
        <img :src="imageUrl" :alt="imageName" class="preview-image" />
      </VCardText>
      <VCardActions>
        <VBtn prepend-icon="mdi-download" variant="outlined" @click="handleDownload"> 下载 </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>

<script setup>
import { computed } from 'vue'

// 接收props
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  imageUrl: {
    type: String,
    default: '',
  },
  imageName: {
    type: String,
    default: '',
  },
})

// 定义emits
const emit = defineEmits(['close', 'download'])

// 计算属性
const showDialog = computed({
  get: () => props.show,
  set: (value) => {
    if (!value) {
      emit('close')
    }
  }
})

// 事件处理
const handleClose = () => {
  emit('close')
}

const handleDownload = () => {
  emit('download', props.imageUrl, props.imageName)
}
</script>

<style scoped>
.preview-image {
  width: 100%;
  height: auto;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 8px;
}
</style>
