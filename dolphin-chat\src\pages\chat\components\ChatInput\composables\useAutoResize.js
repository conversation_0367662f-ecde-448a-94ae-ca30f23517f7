import { ref, nextTick } from 'vue'

/**
 * 自动调整大小相关逻辑
 */
export function useAutoResize(externalTextareaRef) {
  const textareaRef = externalTextareaRef || ref(null)

  /**
   * 自动调整textarea高度
   */
  const autoResize = () => {
    nextTick(() => {
      // 获取实际的 textarea 元素
      const textarea = textareaRef.value?.textareaRef || textareaRef.value
      if (textarea) {
        // 重置高度以获取正确的scrollHeight
        textarea.style.height = 'auto'
        // 设置新高度，最小40px，最大120px
        const newHeight = Math.min(Math.max(textarea.scrollHeight, 40), 120)
        textarea.style.height = newHeight + 'px'
      }
    })
  }

  /**
   * 重置textarea高度到初始状态
   */
  const resetTextareaHeight = () => {
    nextTick(() => {
      // 获取实际的 textarea 元素
      const textarea = textareaRef.value?.textareaRef || textareaRef.value
      if (textarea) {
        // 重置到初始高度
        textarea.style.height = '40px'
      }
    })
  }

  /**
   * 滚动到底部的方法
   */
  const scrollToBottomImmediate = () => {
    nextTick(() => {
      const chatMessagesContainer = document.querySelector('.chat-messages')
      if (chatMessagesContainer) {
        // 使用平滑滚动到底部，确保内容完全可见
        // 添加额外的偏移量确保最后的内容不被输入框遮挡
        const scrollHeight = chatMessagesContainer.scrollHeight
        const clientHeight = chatMessagesContainer.clientHeight
        const maxScrollTop = scrollHeight - clientHeight

        chatMessagesContainer.scrollTo({
          top: maxScrollTop + 50, // 额外增加50px确保完全可见
          behavior: 'smooth',
        })
      
      }
    })
  }

  return {
    // 响应式数据
    textareaRef,

    // 方法
    autoResize,
    resetTextareaHeight,
    scrollToBottomImmediate,
  }
}
