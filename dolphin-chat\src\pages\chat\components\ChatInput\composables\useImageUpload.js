import { ref } from 'vue'
import { FileService } from '@/api/file'
import { API_CONFIG } from '@/api/constants'

/**
 * 图片上传相关逻辑
 */
export function useImageUpload() {
  // 响应式数据
  const attachedImages = ref([]) // 存储待发送的图片
  const showImageDialog = ref(false)
  const selectedImage = ref(null)
  const isUploading = ref(false) // 是否正在上传
  const uploadProgress = ref(0) // 上传进度 (0-100)
  const errorMessage = ref('') // 错误信息
  const showError = ref(false) // 是否显示错误

  /**
   * 添加图片到附件列表
   * @param {File} file - 图片文件
   */
  const addImageToAttachments = (file) => {
    // 检查图片数量限制
    if (attachedImages.value.length >= API_CONFIG.MAX_IMAGES_PER_MESSAGE) {
      showErrorMessage(`最多只能上传${API_CONFIG.MAX_IMAGES_PER_MESSAGE}张图片`)
      return
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      const imageData = {
        url: e.target.result,
        file: file,
        name: file.name,
        id: Date.now() + Math.random(), // 临时ID
        uploading: false, // 上传状态
        progress: 0, // 上传进度
      }
      attachedImages.value.push(imageData)

      // 自动开始上传
      startImageUpload(imageData)
    }
    reader.readAsDataURL(file)
  }

  /**
   * 开始上传图片
   * @param {Object} imageData - 图片数据
   */
  const startImageUpload = async (imageData) => {
    // 设置上传状态
    imageData.uploading = true
    imageData.progress = 0
    isUploading.value = true

    try {
      const result = await FileService.uploadChatImage(imageData.file, {
        onProgress: (progress) => {
          imageData.progress = progress
          uploadProgress.value = progress
        }
      })

      if (result.success) {
        // 上传成功，更新图片数据
        imageData.uploading = false
        imageData.uploaded = true
        imageData.serverData = result.data

      } else {
        // 上传失败，显示错误提示
        imageData.uploading = false
        imageData.uploadError = result.error
        console.error('图片上传失败:', result.error)

        // 显示错误提示
        showErrorMessage(result.error || '图片上传失败')

        // 移除失败的图片
        removeAttachedImage(imageData.id)
      }
    } catch (error) {
      imageData.uploading = false
      imageData.uploadError = error.message
      console.error('图片上传异常:', error)

      // 显示错误提示
      showErrorMessage(error.message || '图片上传异常')

      // 移除失败的图片
      removeAttachedImage(imageData.id)
    } finally {
      // 检查是否还有其他图片在上传
      const stillUploading = attachedImages.value.some(img => img.uploading)
      if (!stillUploading) {
        isUploading.value = false
        uploadProgress.value = 0
      }
    }
  }

  /**
   * 移除附件图片
   * @param {string|number} imageId - 图片ID
   */
  const removeAttachedImage = (imageId) => {
    attachedImages.value = attachedImages.value.filter((img) => img.id !== imageId)
  }

  /**
   * 清空所有附件
   */
  const clearAttachedImages = () => {
    attachedImages.value = []
  }

  /**
   * 处理文件选择
   * @param {Event} event - 文件选择事件
   */
  const handleFileSelect = (event) => {
    const files = event.target.files
    if (files && files.length > 0) {
      const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'))

      // 检查总数量限制
      const remainingSlots = API_CONFIG.MAX_IMAGES_PER_MESSAGE - attachedImages.value.length
      if (remainingSlots <= 0) {
        showErrorMessage(`最多只能上传${API_CONFIG.MAX_IMAGES_PER_MESSAGE}张图片`)
        return
      }

      // 如果选择的图片数量超过剩余槽位，只处理前面的图片
      const filesToProcess = imageFiles.slice(0, remainingSlots)
      if (imageFiles.length > remainingSlots) {
        showErrorMessage(`只能再添加${remainingSlots}张图片，已为您选择前${remainingSlots}张`)
      }

      filesToProcess.forEach((file) => {
        addImageToAttachments(file)
      })
    }
  }

  /**
   * 处理剪贴板粘贴
   * @param {ClipboardEvent} event - 粘贴事件
   */
  const handlePaste = (event) => {
    const items = event.clipboardData?.items
    if (!items) return

    // 检查数量限制
    if (attachedImages.value.length >= API_CONFIG.MAX_IMAGES_PER_MESSAGE) {
      showErrorMessage(`最多只能上传${API_CONFIG.MAX_IMAGES_PER_MESSAGE}张图片`)
      return
    }

    for (let i = 0; i < items.length; i++) {
      const item = items[i]
      if (item.type.startsWith('image/')) {
        event.preventDefault()
        const file = item.getAsFile()
        if (file) {
          addImageToAttachments(file)
        }
        break // 粘贴时只处理第一张图片
      }
    }
  }

  /**
   * 处理拖拽悬停
   * @param {DragEvent} event - 拖拽事件
   */
  const handleDragOver = (event) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'copy'
  }

  /**
   * 处理拖拽放置
   * @param {DragEvent} event - 拖拽事件
   */
  const handleDrop = (event) => {
    event.preventDefault()
    const files = event.dataTransfer.files
    if (files && files.length > 0) {
      const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'))

      // 检查总数量限制
      const remainingSlots = API_CONFIG.MAX_IMAGES_PER_MESSAGE - attachedImages.value.length
      if (remainingSlots <= 0) {
        showErrorMessage(`最多只能上传${API_CONFIG.MAX_IMAGES_PER_MESSAGE}张图片`)
        return
      }

      // 如果拖拽的图片数量超过剩余槽位，只处理前面的图片
      const filesToProcess = imageFiles.slice(0, remainingSlots)
      if (imageFiles.length > remainingSlots) {
        showErrorMessage(`只能再添加${remainingSlots}张图片，已为您选择前${remainingSlots}张`)
      }

      filesToProcess.forEach((file) => {
        addImageToAttachments(file)
      })
    }
  }

  /**
   * 打开图片预览
   * @param {Object} image - 图片数据
   */
  const openImagePreview = (image) => {
    selectedImage.value = image
    showImageDialog.value = true
  }

  /**
   * 关闭图片预览
   */
  const closeImagePreview = () => {
    showImageDialog.value = false
    selectedImage.value = null
  }

  /**
   * 下载图片
   * @param {Object} image - 图片数据
   */
  const downloadImage = (image) => {
    if (image && image.url) {
      const link = document.createElement('a')
      link.href = image.url
      link.download = image.name || 'image.png'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  /**
   * 显示错误消息
   * @param {string} message - 错误消息
   */
  const showErrorMessage = (message) => {
    errorMessage.value = message
    showError.value = true

    // 3秒后自动隐藏错误提示
    setTimeout(() => {
      showError.value = false
      errorMessage.value = ''
    }, 3000)
  }

  /**
   * 隐藏错误消息
   */
  const hideErrorMessage = () => {
    showError.value = false
    errorMessage.value = ''
  }

  /**
   * 上传图片到服务器（带进度）
   * @param {File} file - 图片文件
   * @returns {Promise} 上传结果
   */
  const uploadImageWithProgress = async (file) => {
    isUploading.value = true
    uploadProgress.value = 0

    try {
      const result = await FileService.uploadChatImage(file, {
        onProgress: (progress) => {
          uploadProgress.value = progress
        }
      })

      return result
    } finally {
      isUploading.value = false
      uploadProgress.value = 0
    }
  }

  return {
    // 响应式数据
    attachedImages,
    showImageDialog,
    selectedImage,
    isUploading,
    uploadProgress,
    errorMessage,
    showError,

    // 方法
    addImageToAttachments,
    removeAttachedImage,
    clearAttachedImages,
    handleFileSelect,
    handlePaste,
    handleDragOver,
    handleDrop,
    openImagePreview,
    closeImagePreview,
    downloadImage,
    uploadImageWithProgress,
    startImageUpload,
    showErrorMessage,
    hideErrorMessage,
  }
}
