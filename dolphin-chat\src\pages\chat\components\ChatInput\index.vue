<script setup>
import { ref, computed, onMounted } from 'vue'
import { useChatStore } from '@/stores/baseStore'

// 导入子组件
import ImageUploadArea from './components/ImageUploadArea.vue'
import MessageTextarea from './components/MessageTextarea.vue'
import ActionButtons from './components/ActionButtons.vue'
import ImagePreviewDialog from './components/ImagePreviewDialog.vue'

// 导入 composables
import { useImageUpload } from './composables/useImageUpload'
import { useAutoResize } from './composables/useAutoResize'
import { useMessageSend } from './composables/useMessageSend'

// 接收父组件传递的侧边栏状态
const props = defineProps({
  sidebarCollapsed: {
    type: Boolean,
    default: false,
  },
})

// 使用聊天store
const chatStore = useChatStore()

// 计算属性 - 监听照片墙和画布状态
const showPhotoWall = computed(() => chatStore.showPhotoWall)
const showImageCanvas = computed(() => chatStore.showImageCanvas)

// 响应式数据
const message = ref('')
const fileInputRef = ref(null)
const messageTextareaRef = ref(null)

// 使用 composables
const {
  attachedImages,
  showImageDialog,
  selectedImage,
  isUploading,
  uploadProgress,
  errorMessage,
  showError,
  addImageToAttachments,
  removeAttachedImage,
  clearAttachedImages,
  handleFileSelect,
  handlePaste,
  handleDragOver,
  handleDrop,
  openImagePreview,
  closeImagePreview,
  downloadImage,
  hideErrorMessage,
} = useImageUpload()

const {
  autoResize,
  resetTextareaHeight,
  scrollToBottomImmediate,
} = useAutoResize(messageTextareaRef)

const {
  deepThinkActive,
  webSearchActive,
  isStreaming,
  handleSend,
  handleDeepThink,
  handleWebSearch,
  handleKeydown,
  stopStreaming,
} = useMessageSend()

// 处理发送消息
const handleSendMessage = async () => {
  await handleSend(
    message.value,
    attachedImages.value,
    clearAttachedImages,
    resetTextareaHeight,
    scrollToBottomImmediate
  )
  // 清空输入框
  message.value = ''
}

// 处理键盘事件
const handleTextareaKeydown = (event) => {
  handleKeydown(event, handleSendMessage)
}

// 处理附件点击
const handleAttachment = () => {
  fileInputRef.value?.click()
}

// 处理文件选择后清空input
const handleFileSelectAndClear = (event) => {
  handleFileSelect(event)
  // 清空input
  if (fileInputRef.value) {
    fileInputRef.value.value = ''
  }
}

// 计算发送按钮是否禁用
const isSendDisabled = computed(() => {
  // 如果没有消息内容且没有图片，禁用发送
  if (!message.value.trim() && attachedImages.value.length === 0) {
    return true
  }

  // 如果正在加载中，禁用发送
  if (chatStore.isLoading) {
    return true
  }

  // 如果有图片正在上传，禁用发送
  const hasUploadingImages = attachedImages.value.some(img => img.uploading)
  if (hasUploadingImages) {
    return true
  }

  // 如果有图片上传失败，禁用发送
  const hasFailedImages = attachedImages.value.some(img => img.uploadError)
  if (hasFailedImages) {
    return true
  }

  return false
})

// 计算输入框占位符
const inputPlaceholder = computed(() => {
  // 检查是否有图片正在上传
  const hasUploadingImages = attachedImages.value.some(img => img.uploading)
  if (hasUploadingImages) {
    return '图片上传中，请稍候...'
  }

  // 检查是否有图片上传失败
  const hasFailedImages = attachedImages.value.some(img => img.uploadError)
  if (hasFailedImages) {
    return '图片上传失败，请重新上传...'
  }

  // 正常状态
  return attachedImages.value.length > 0 ? '添加描述（可选）...' : '给Dolphin AI 发送消息...'
})

// 组件挂载时初始化textarea高度
onMounted(() => {
  autoResize()
})
</script>

<template>
  <div
    class="chat-input-container"
    :class="{
      'sidebar-collapsed': sidebarCollapsed,
      'with-photo-wall': showPhotoWall,
      'with-image-canvas': showImageCanvas,
    }">
    <VContainer>
      <div class="input-wrapper">
        <div class="deepseek-input-card">
          <!-- 图片上传区域 -->
          <ImageUploadArea
            :attached-images="attachedImages"
            :is-uploading="isUploading"
            :upload-progress="uploadProgress"
            @upload-click="handleAttachment"
            @drag-over="handleDragOver"
            @drop="handleDrop"
            @preview-image="openImagePreview"
            @remove-image="removeAttachedImage" />

          <!-- 分割线 -->
          <VDivider />

          <!-- 消息输入框区域 -->
          <MessageTextarea
            ref="messageTextareaRef"
            v-model="message"
            :placeholder="inputPlaceholder"
            @keydown="handleTextareaKeydown"
            @input="autoResize"
            @paste="handlePaste" />

          <!-- 底部按钮行 -->
          <ActionButtons
            :deep-think-active="deepThinkActive"
            :web-search-active="webSearchActive"
            :disabled="isSendDisabled"
            :is-loading="chatStore.isLoading"
            :is-streaming="isStreaming"
            @deep-think="handleDeepThink"
            @web-search="handleWebSearch"
            @attachment="handleAttachment"
            @send="handleSendMessage"
            @stop="stopStreaming" />
        </div>
      </div>
    </VContainer>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      accept="image/*"
      multiple
      style="display: none"
      @change="handleFileSelectAndClear" />

    <!-- 图片预览对话框 -->
    <ImagePreviewDialog
      :show="showImageDialog"
      :selected-image="selectedImage"
      @close="closeImagePreview"
      @download="downloadImage" />

    <!-- 错误提示 Snackbar -->
    <VSnackbar
      v-model="showError"
      :timeout="3000"
      color="error"
      location="top"
      @click="hideErrorMessage">
      <VIcon icon="mdi-alert-circle" class="me-2" />
      {{ errorMessage }}
      <template #actions>
        <VBtn
          icon="mdi-close"
          size="small"
          variant="text"
          @click="hideErrorMessage" />
      </template>
    </VSnackbar>
  </div>
</template>

<style scoped>
.chat-input-container {
  position: fixed;
  bottom: 0;
  left: 280px; /* 侧边栏展开时的宽度 */
  right: 0;
  background-color: var(--app-bg-secondary);
  padding: 8px 0;
  z-index: 10;
  transition: left 0.3s ease, right 0.3s ease, background-color 0.3s ease;
}

/* 侧边栏收缩时的样式 */
.chat-input-container.sidebar-collapsed {
  left: 60px; /* 侧边栏收缩时的宽度 */
}

/* 照片墙打开时的样式 */
.chat-input-container.with-photo-wall {
  right: 320px; /* 照片墙的宽度 */
}

/* 画布打开时的样式 */
.chat-input-container.with-image-canvas {
  right: 500px; /* 画布的宽度 */
}

/* 同时打开照片墙和画布时的样式 */
.chat-input-container.with-photo-wall.with-image-canvas {
  right: 820px; /* 320px + 500px */
}

/* 同时有侧边栏收缩和照片墙打开的样式 */
.chat-input-container.sidebar-collapsed.with-photo-wall {
  left: 60px;
  right: 320px;
}

.input-wrapper {
  max-width: 800px;
  margin: 0 auto;
}

.deepseek-input-card {
  border: 1px solid rgba(102, 126, 234, 0.15);
  border-radius: 28px;
  background: var(--app-bg-primary);
  overflow: hidden;
  transition: border-color 0.2s ease, background-color 0.3s ease, box-shadow 0.2s ease;
  box-shadow: 0 2px 8px var(--app-shadow-light);
}

.deepseek-input-card:hover {
  border-color: rgba(102, 126, 234, 0.25);
}

.deepseek-input-card:focus-within {
  border-color: rgba(102, 126, 234, 0.4);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

/* 深色主题适配 */
[data-theme="dark"] .deepseek-input-card {
  border-color: rgba(255, 255, 255, 0.15);
  background: #000000;
}

[data-theme="dark"] .deepseek-input-card:hover {
  border-color: rgba(255, 255, 255, 0.25);
}

[data-theme="dark"] .deepseek-input-card:focus-within {
  border-color: rgba(33, 150, 243, 0.6);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-input-container {
    left: 0;
    right: 0;
    padding: 6px 0;
  }

  /* 移动端照片墙打开时不改变输入框位置 */
  .chat-input-container.with-photo-wall {
    right: 0;
  }
}
</style>
