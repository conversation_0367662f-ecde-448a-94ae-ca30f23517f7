/**
 * 消息操作相关的 composable
 */
export function useMessageActions() {
  // 复制消息内容
  const copyMessage = (content) => {
    navigator.clipboard.writeText(content)
    console.log("消息已复制")
  }

  // 重新生成消息
  const regenerateMessage = () => {
    console.log("重新生成消息")
    // 这里可以添加重新生成消息的逻辑
  }

  // 格式化时间
  const formatTime = (timestamp) => {
    try {
      // 处理不同类型的时间戳
      let date
      if (timestamp instanceof Date) {
        date = timestamp
      } else if (typeof timestamp === 'string') {
        date = new Date(timestamp)
      } else if (typeof timestamp === 'number') {
        date = new Date(timestamp)
      } else {
        date = new Date()
      }

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return '--:--'
      }

      return date.toLocaleTimeString("zh-CN", {
        hour: "2-digit",
        minute: "2-digit",
      })
    } catch (error) {
      console.warn('时间格式化失败:', error)
      return '--:--'
    }
  }

  return {
    // 方法
    copyMessage,
    regenerateMessage,
    formatTime,
  }
}
